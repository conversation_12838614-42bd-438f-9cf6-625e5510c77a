:root {
  --primary-green: #2a4858;
  --primary-brown: #8b7355;

  --accent-sage: #2a4858ac;
  --accent-terracotta: #967259;
  --accent-light-brown: #a68c69;

  --neutral-cream: #f2e8dc;
  --neutral-beige: #d8c3a5;
  --neutral-light: #f6f4f0;
  --neutral-dark: #3a3026;

  --text-dark: #3a3026;
  --text-medium: #5d4e41;
  --text-light: #f6f4f0;
  
  /* Additional variables for consistency */
  --success-color: #4caf50;
  --warning-color: #ff9800;
  --error-color: #f44336;
  --info-color: #2196f3;
  
  /* Spacing variables */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 2rem;
  --spacing-xl: 4rem;
  
  /* Border radius */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --border-radius-lg: 16px;
  --border-radius-xl: 24px;
  --border-radius-round: 50%;
  
  /* Box shadows */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 12px 24px rgba(0, 0, 0, 0.1);
}

html {
  scroll-behavior: smooth;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-light);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-green);
  border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--accent-sage);
}

/* Global styles */
* {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  scroll-behavior: smooth;
  text-decoration: none;
}

body {
  background-color: var(--neutral-light);
}
