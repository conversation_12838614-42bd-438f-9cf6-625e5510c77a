.faq-container {
  padding: 20px;
}

.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: var(--border-radius);
}

.alert-success {
  background-color: var(--success);
  color: var(--text-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.alert-danger {
  background-color: var(--danger);
  color: var(--text-light);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.form-container {
  display: none; /* Hide by default */
  background-color: var(--neutral-light);
  padding: 20px;
  border-radius: var(--border-radius);
  margin-bottom: 30px;
  box-shadow: var(--box-shadow);
}

.form-container.active {
  display: block;
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 8px;
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  font-size: 14px;
  background-color: var(--text-light);
  color: var(--text-dark);
}

.form-control:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 2px var(--accent-sage);
}

.form-switch {
  display: flex;
  align-items: center;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 14px;
  transition: background-color var(--transition-speed);
}

.btn-primary {
  background-color: var(--primary-green);
  color: var(--text-light);
}

.btn-primary:hover {
  background-color: var(--accent-sage);
}

.btn-secondary {
  background-color: var(--neutral-beige);
  color: var(--text-dark);
}

.btn-secondary:hover {
  background-color: var(--neutral-cream);
}

.btn-danger {
  background-color: var(--danger);
  color: var(--text-light);
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.faq-list {
  margin-top: 20px;
}

.category-header {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 10px 15px;
  margin-top: 20px;
  font-weight: bold;
  border-radius: var(--border-radius);
}

.faq-item {
  border-left: 4px solid var(--primary-green);
  margin-bottom: 15px;
  padding: 15px;
  background-color: var(--text-light);
  box-shadow: var(--box-shadow);
  border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

.faq-item:hover {
  background-color: var(--neutral-light);
}

.faq-content {
  flex: 1;
}

.faq-actions {
  display: flex;
  gap: 10px;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.text-small {
  font-size: 12px;
  color: var(--text-medium);
}

textarea.form-control {
  min-height: 100px;
  resize: vertical;
  line-height: 1.5;
}

.page-title {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--neutral-beige);
  color: var(--text-dark);
}

.add-faq-btn {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 10px 20px;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background-color var(--transition-speed);
}

.add-faq-btn:hover {
  background-color: var(--accent-sage);
}

.add-faq-btn i {
  transition: transform var(--transition-speed);
}

.add-faq-btn.active i {
  transform: rotate(45deg);
}

/* Status indicator styles */
.status-active {
  color: var(--success);
}

.status-inactive {
  color: var(--text-medium);
}
