/* Community Components CSS */
/* Reusable components and UI elements */

/* Loading States */
.loading-spinner {
    display: inline-block;
    width: 40px;
    height: 40px;
    border: 4px solid var(--neutral-light);
    border-radius: 50%;
    border-top-color: var(--primary-green);
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

.loading-spinner.large {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-overlay .loading-content {
    text-align: center;
    color: var(--text-dark);
}

.loading-overlay .loading-content h3 {
    margin-top: 1rem;
    font-size: 1.2rem;
    font-weight: 600;
}

.loading-overlay .loading-content p {
    margin-top: 0.5rem;
    color: var(--text-medium);
}

/* Notification Toast */
.notification-toast {
    position: fixed;
    top: 2rem;
    right: 2rem;
    max-width: 400px;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-xl);
    border-left: 4px solid var(--primary-green);
    padding: 1.5rem;
    z-index: 10000;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.notification-toast.show {
    transform: translateX(0);
}

.notification-toast.success {
    border-left-color: var(--success-color);
}

.notification-toast.error {
    border-left-color: var(--error-color);
}

.notification-toast.warning {
    border-left-color: var(--warning-color);
}

.notification-toast.info {
    border-left-color: var(--info-color);
}

.toast-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.toast-title {
    font-weight: 600;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.toast-close {
    background: none;
    border: none;
    color: var(--text-medium);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.3s ease;
}

.toast-close:hover {
    background-color: var(--neutral-light);
}

.toast-message {
    color: var(--text-medium);
    line-height: 1.5;
}

/* Modal Component */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: var(--border-radius-lg);
    max-width: 90%;
    max-height: 90%;
    overflow-y: auto;
    box-shadow: var(--shadow-xl);
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 2rem 2rem 1rem;
    border-bottom: 1px solid var(--neutral-beige);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--text-medium);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: var(--neutral-light);
    color: var(--text-dark);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1rem 2rem 2rem;
    border-top: 1px solid var(--neutral-beige);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Accordion Component */
.accordion {
    border: 1px solid var(--neutral-beige);
    border-radius: var(--border-radius-lg);
    overflow: hidden;
}

.accordion-item {
    border-bottom: 1px solid var(--neutral-beige);
}

.accordion-item:last-child {
    border-bottom: none;
}

.accordion-header {
    background: var(--neutral-light);
    padding: 1.5rem;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: background-color 0.3s ease;
}

.accordion-header:hover {
    background: var(--neutral-beige);
}

.accordion-header.active {
    background: var(--primary-green);
    color: white;
}

.accordion-title {
    font-weight: 600;
    font-size: 1.1rem;
    margin: 0;
}

.accordion-icon {
    transition: transform 0.3s ease;
}

.accordion-header.active .accordion-icon {
    transform: rotate(180deg);
}

.accordion-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
}

.accordion-content.active {
    max-height: 500px;
}

.accordion-body {
    padding: 2rem;
    background: white;
}

/* Tabs Component */
.tabs-container {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    overflow: hidden;
}

.tabs-nav {
    display: flex;
    background: var(--neutral-light);
    border-bottom: 1px solid var(--neutral-beige);
}

.tab-button {
    flex: 1;
    padding: 1rem 2rem;
    background: none;
    border: none;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-medium);
    transition: all 0.3s ease;
    position: relative;
}

.tab-button:hover {
    background: var(--neutral-beige);
    color: var(--text-dark);
}

.tab-button.active {
    background: white;
    color: var(--primary-green);
}

.tab-button.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--primary-green);
}

.tab-content {
    padding: 2rem;
    display: none;
}

.tab-content.active {
    display: block;
}

/* Progress Bar */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--neutral-light);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--accent-sage) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    width: 0%;
}

.progress-bar.large {
    height: 12px;
}

.progress-bar.small {
    height: 4px;
}

/* Badge Component */
.badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    border-radius: var(--border-radius-sm);
    line-height: 1;
}

.badge.primary {
    background: var(--primary-green);
    color: white;
}

.badge.secondary {
    background: var(--accent-terracotta);
    color: white;
}

.badge.success {
    background: var(--success-color);
    color: white;
}

.badge.warning {
    background: var(--warning-color);
    color: white;
}

.badge.error {
    background: var(--error-color);
    color: white;
}

.badge.outline {
    background: transparent;
    border: 1px solid currentColor;
}

/* Tooltip Component */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip-content {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-dark);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.875rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
    margin-bottom: 0.5rem;
}

.tooltip-content::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: var(--text-dark);
}

.tooltip:hover .tooltip-content {
    opacity: 1;
    visibility: visible;
}

/* Responsive Utilities */
@media (max-width: 768px) {
    .notification-toast {
        top: 1rem;
        right: 1rem;
        left: 1rem;
        max-width: none;
    }
    
    .modal-content {
        max-width: 95%;
        margin: 1rem;
    }
    
    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1.5rem;
    }
    
    .tabs-nav {
        flex-direction: column;
    }
    
    .tab-button {
        text-align: left;
    }
    
    .accordion-header {
        padding: 1rem;
    }
    
    .accordion-body {
        padding: 1.5rem;
    }
}
