/* Import Header and <PERSON><PERSON> Styles */
@import url("header.css");
@import url("footer.css");
@import url("components.css");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Inter", -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background: var(--neutral-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

/* Hero Section */
.hero-section {
  background: #000; /* video background */
  color: var(--text-light);
  padding: var(--spacing-xl) 0 3rem;
  position: relative;
  overflow: hidden;
  min-height: 100vh;
  display: flex;
  align-items: center;
}

.hero-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" patternUnits="userSpaceOnUse" width="100" height="100"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.02)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.03)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.5;
}

.hero-content {
  position: relative;
  z-index: 2;
  text-align: center;
}

/* Make the video fill the hero as a cover */
.hero-section .hero-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: 0;
  filter: brightness(0.8);
}

/* Optional gradient overlay above video for better text readability */
.hero-section::after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.089) 0%,
    rgba(0, 0, 0, 0.231) 60%,
    rgba(0, 0, 0, 0.249) 100%
  );
  z-index: 1;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 3rem);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  line-height: 1;
}

.hero-subtitle {
  font-size: clamp(0.9rem, 1.5vw, 1rem);
  opacity: 0.9;
  margin-bottom: var(--spacing-lg);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-cta {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: var(--primary-brown);
  color: var(--text-light);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s ease;
}

.hero-cta:hover {
  background: var(--accent-terracotta);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Statistics Section (moved and made smaller) */
.stats-section {
  background: var(--neutral-light);
  padding: var(--spacing-lg) 0;
  border-bottom: 1px solid var(--neutral-beige);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
  max-width: 800px;
  margin: 0 auto;
}

.stat-card {
  background: white;
  border: 1px solid var(--neutral-beige);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    45deg,
    transparent,
    rgba(42, 72, 88, 0.1),
    transparent
  );
  transform: rotate(45deg);
  transition: transform 0.6s ease;
  opacity: 0;
}

.stat-card:hover::before {
  opacity: 1;
  transform: rotate(45deg) translateX(100%);
}

.stat-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.stat-icon {
  font-size: 1.5rem;
  margin-bottom: var(--spacing-xs);
  color: var(--primary-green);
}

.stat-number {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: var(--spacing-xs);
  color: var(--text-dark);
}

.stat-label {
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 1px;
  color: var(--text-medium);
  font-weight: 500;
}

/* About Section */
.about-section {
  padding: var(--spacing-xl) 0;
  background: var(--neutral-light);
}

.section-title {
  font-size: clamp(2rem, 4vw, 2.5rem);
  font-weight: 600;
  margin-bottom: var(--spacing-lg);
  text-align: center;
  color: var(--text-dark);
}

.about-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: var(--spacing-xl);
  align-items: center;
  margin-bottom: var(--spacing-xl);
}

.about-text {
  color: var(--text-medium);
  font-size: 1.1rem;
  line-height: 1.7;
}

.about-text p {
  margin-bottom: var(--spacing-md);
}

.about-image {
  position: relative;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.about-image img {
  width: 100%;
  height: 300px;
  object-fit: cover;
}

.mission-vision {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-lg);
}

.mission-card,
.vision-card {
  background: var(--neutral-cream);
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  border-left: 4px solid var(--primary-green);
  transition: all 0.3s ease;
}

.mission-card:hover,
.vision-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-md);
}

.card-title {
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 1.1rem;
}

.card-text {
  font-size: 0.95rem;
  color: var(--text-medium);
  line-height: 1.6;
}

/* Programs Section */
.programs-section {
  padding: var(--spacing-xl) 0;
  background: var(--neutral-cream);
}

.programs-intro {
  text-align: center;
  margin-bottom: var(--spacing-xl);
  color: var(--text-medium);
}

.programs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.program-card {
  background: white;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
  position: relative;
}

.program-card:hover {
  transform: translateY(-8px);
  box-shadow: var(--shadow-xl);
}

.program-image {
  position: relative;
  height: 160px;
  background: linear-gradient(
    135deg,
    var(--primary-brown),
    var(--accent-terracotta)
  );
  display: flex;
  align-items: center;
  justify-content: center;
}

.program-icon {
  font-size: 2.5rem;
  color: var(--text-light);
}

.program-content {
  padding: var(--spacing-lg);
}

.program-category {
  display: inline-block;
  background: var(--accent-sage);
  color: var(--text-light);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--border-radius-sm);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  margin-bottom: var(--spacing-sm);
}

.program-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-dark);
  line-height: 1.3;
}

.program-description {
  font-size: 0.9rem;
  color: var(--text-medium);
  line-height: 1.6;
  margin-bottom: var(--spacing-md);
}

.program-stats {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.program-stat {
  font-size: 0.8rem;
  color: var(--text-medium);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.program-stat i {
  color: var(--primary-brown);
}

.learn-more-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--primary-green);
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.3s ease;
}

.learn-more-btn:hover {
  color: var(--accent-terracotta);
}

/* Parallax Section */
.parallax-section {
  height: 60vh;
  background-image: url("../../uploads/impact/her2.jpg");
  background-attachment: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.parallax-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(42, 72, 88, 0.5);
  z-index: 1;
}

.parallax-content {
  position: relative;
  z-index: 2;
  text-align: center;
  color: var(--text-light);
  transform: translateZ(0);
  will-change: transform;
}

.parallax-title {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 700;
  margin-bottom: var(--spacing-md);
  line-height: 1.2;
  transform: translateZ(0);
}

.parallax-subtitle {
  font-size: clamp(1rem, 2vw, 1.2rem);
  opacity: 0.9;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
  transform: translateZ(0);
}

/* Activities Section */
.activities-section {
  padding: var(--spacing-xl) 0;
  background: var(--neutral-light);
}

.activities-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.activity-card {
  position: relative;
  height: 240px;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  color: var(--text-light);
  cursor: pointer;
  transition: transform 0.3s ease, background-position 0.6s ease;
  border: 1px solid var(--neutral-beige);
  box-shadow: var(--shadow-sm);
  will-change: transform;
}

.activity-card::before {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(
    180deg,
    rgba(0, 0, 0, 0.06) 0%,
    rgba(0, 0, 0, 0.15) 60%,
    rgba(0, 0, 0, 0.25) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.activity-card:hover {
  transform: scale(1.02);
  background-position: center 45%;
  box-shadow: var(--shadow-md);
}

.activity-card:hover::before {
  opacity: 1;
}

.activity-card:focus-within {
  outline: none;
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.activity-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: var(--spacing-md) var(--spacing-lg);
  background: linear-gradient(transparent, rgba(42, 72, 88, 0.75));
  z-index: 2;
  transition: transform 0.3s ease;
  min-height: 42%;
  max-height: 52%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

.activity-card:hover .activity-content,
.activity-card:focus-within .activity-content {
  transform: translateY(-2px);
}

.activity-title {
  font-size: 1.1rem;
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.activity-description {
  font-size: 0.9rem;
  opacity: 0.9;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0,0,0,0.4);
}

/* Testimonials Section */
.testimonials-section {
  padding: var(--spacing-xl) 0;
  background: var(--neutral-cream);
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  margin-top: var(--spacing-lg);
}

.testimonial-card {
  background: white;
  padding: var(--spacing-lg);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  transition: transform 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-3px);
}

.quote-icon {
  font-size: 2rem;
  color: var(--neutral-beige);
  margin-bottom: var(--spacing-md);
}

.testimonial-text {
  font-style: italic;
  color: var(--text-medium);
  margin-bottom: var(--spacing-md);
  line-height: 1.6;
}

.testimonial-author {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.author-avatar {
  width: 40px;
  height: 40px;
  background: linear-gradient(
    135deg,
    var(--primary-green),
    var(--primary-brown)
  );
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-light);
  font-weight: 600;
  font-size: 0.9rem;
}

.author-info h4 {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 2px;
}

.author-info p {
  font-size: 0.8rem;
  color: var(--text-medium);
}

.star-rating {
  color: #fbbf24;
  margin-top: var(--spacing-sm);
}

/* Imigongo Design Section */
.imigongo-section {
  width: 100%;
}

.imigongo-pattern {
  height: 200px;
  background-image: url("../../uploads/impact/imigongo.png");
  width: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: repeat;
  position: relative;
  overflow: hidden;
}

/* CTA Button */
.cta-button {
  display: inline-block;
  background: var(--primary-brown);
  color: var(--text-light);
  padding: var(--spacing-md) var(--spacing-lg);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  text-align: center;
}

.cta-button:hover {
  background: var(--accent-terracotta);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.text-center {
  text-align: center;
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeInUp 0.6s ease-out;
}

.scroll-animate {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease;
}

.scroll-animate.visible {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .parallax-section {
    background-attachment: scroll;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--spacing-sm);
  }

  .hero-section {
    min-height: 70vh;
    padding: var(--spacing-lg) 0 2rem;
  }

  .hero-title {
    font-size: 2.5rem;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }

  .about-content {
    grid-template-columns: 1fr;
    text-align: center;
  }

  .mission-vision {
    grid-template-columns: 1fr;
  }

  .programs-grid,
  .activities-grid,
  .testimonials-grid {
    grid-template-columns: 1fr;
  }

  .parallax-section {
    height: 50vh;
  }

  .parallax-title {
    font-size: 2rem;
  }

  .imigongo-pattern {
    height: 150px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .stat-card {
    padding: var(--spacing-sm);
  }

  .stat-number {
    font-size: 1.5rem;
  }

  .stat-icon {
    font-size: 1.2rem;
  }

  .parallax-section {
    height: 40vh;
  }

  .imigongo-pattern {
    height: 120px;
  }
}

/* Partners Slider Section */
.partners-section {
  padding: var(--spacing-xl) 0;
  background: var(--neutral-light);
}

.partners-slider {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-md);
}

.slider-container {
  display: flex;
  gap: var(--spacing-lg);
  overflow-x: auto;
  scroll-behavior: smooth;
  padding: var(--spacing-md) 0;
  /* hide scrollbar */
  scrollbar-width: none; /* Firefox */
}

.slider-container::-webkit-scrollbar {
  /* WebKit */
  display: none;
}

.slider-item {
  flex: 0 0 auto;
  min-width: 160px;
  max-width: 220px;
  height: 90px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-sm);
  padding: var(--spacing-md);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.slider-item:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-md);
}

.slider-item img {
  max-height: 56px;
  max-width: 100%;
  object-fit: contain;
  opacity: 0.8;
  transition: filter 0.3s ease, opacity 0.3s ease;
}

.slider-item:hover img {
  opacity: 1;
}

.slider-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(4px);
  transition: background 0.3s ease, transform 0.2s ease;
  z-index: 2;
}

.slider-control:hover {
  background: rgba(0, 0, 0, 0.7);
  transform: translateY(-50%) scale(1.05);
}

.slider-prev {
  left: var(--spacing-sm);
}
.slider-next {
  right: var(--spacing-sm);
}

@media (max-width: 768px) {
  .slider-item {
    min-width: 130px;
    height: 80px;
    padding: var(--spacing-sm);
  }

  .slider-item img {
    max-height: 44px;
  }

  .slider-control {
    display: none; /* rely on swipe/drag on mobile */
  }
}
