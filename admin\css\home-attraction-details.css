.content-wrapper {  padding: 1.5rem;  max-width: 1200px; width: 100%;  margin: 0 auto;  }
.content-header {  display: flex;  justify-content: space-between;
  align-items: center;  margin-bottom: 1.5rem;  }
.content-header h1 {  display: flex;  align-items: center;  gap: 0.75rem;
  color: var(--primary-green);  font-size: 1.8rem;
  margin: 0;  }
.content-header h1 i {
  color: var(--accent-terracotta);}
/* Tabs Navigation */.tabs-container {  margin-bottom: 1.5rem;  }
.tabs {  display: flex;
  border-bottom: 2px solid var(--neutral-beige);  margin-bottom: 1.5rem;
}
.tab-btn {  padding: 0.75rem 1.5rem;
  background: none;  border: none;
  font-size: 1rem;  font-weight: 500;
  color: var(--text-medium);  cursor: pointer;
  transition: all var(--transition-speed) ease;  position: relative;
  display: flex;  align-items: center;
  gap: 0.5rem;}
.tab-btn i {
  font-size: 1.1rem;}
.tab-btn:hover {
  color: var(--primary-green);}
.tab-btn.active {
  color: var(--primary-green);  font-weight: 600;
}
.tab-btn.active::after {  content: '';
  position: absolute;  bottom: -2px;
  left: 0;  width: 100%;
  height: 2px;  background-color: var(--primary-green);
}
/* Card Styles with Enhanced Design */.card {
  background-color: white;  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);  margin-bottom: 2rem;
  overflow: hidden;  transition: box-shadow 0.3s ease;
}
.card:hover {  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
.card-header {  padding: 1.25rem 1.5rem;
  border-bottom: 1px solid var(--neutral-beige);  background-color: var(--neutral-light);
  display: flex;  justify-content: space-between;
  align-items: center;}
.card-header h2 {
  margin: 0;  font-size: 1.25rem;
  color: var(--primary-green);  display: flex;
  align-items: center;  gap: 0.5rem;
}
.card-body {  padding: 1.5rem;
}
/* Form Styles with Enhanced Design */.form-group {
  margin-bottom: 1.5rem;}
.form-group label {
  display: block;  margin-bottom: 0.5rem;
  font-weight: 500;  color: var(--text-dark);
}
.form-group input[type="text"],.form-group input[type="url"],
.form-group input[type="number"],.form-group textarea {
  width: 100%;  padding: 0.75rem 1rem;
  border: 1px solid var(--neutral-beige);  border-radius: var(--border-radius);
  background-color: var(--neutral-light);  font-size: 1rem;
  transition: all var(--transition-speed) ease;}
.form-group input[type="text"]:focus,
.form-group input[type="url"]:focus,.form-group input[type="number"]:focus,
.form-group textarea:focus {  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.1);  outline: none;
  background-color: white;}
.form-group textarea {
  resize: vertical;  min-height: 120px;
}
.form-group small {  display: block;
  margin-top: 0.5rem;  color: var(--text-medium);
  font-size: 0.85rem;}
.form-actions {
  margin-top: 2rem;  display: flex;
  justify-content: flex-start;  gap: 1rem;
}
/* Button Styles */.btn {
  display: inline-flex;  align-items: center;
  justify-content: center;  gap: 0.5rem;
  padding: 0.75rem 1.5rem;  border-radius: var(--border-radius);
  font-weight: 500;  font-size: 1rem;
  cursor: pointer;  transition: all var(--transition-speed) ease;
  border: none;}
.btn-primary {
  background-color: var(--primary-green);  color: var(--text-light);
}
.btn-primary:hover {  background-color: var(--accent-sage);
  transform: translateY(-2px);}
.btn-secondary {
  background-color: var(--neutral-light);  color: var(--text-dark);
  border: 1px solid var(--neutral-beige);}
.btn-secondary:hover {
  background-color: var(--neutral-beige);  transform: translateY(-2px);
}
.btn i {  font-size: 0.9rem;
}
/* Alert Styles */.alert {
  padding: 1rem 1.5rem;  margin-bottom: 1.5rem;
  border-radius: var(--border-radius);  font-weight: 500;
  display: flex;  align-items: center;
  gap: 0.75rem;  animation: slideDown 0.3s ease-out forwards;
}
.alert-success {  background-color: rgba(76, 175, 80, 0.1);
  color: #2e7d32;  border-left: 4px solid #2e7d32;
}
.alert-error {  background-color: rgba(244, 67, 54, 0.1);
  color: #d32f2f;  border-left: 4px solid #d32f2f;
}
@keyframes slideDown {  from {
    opacity: 0;    transform: translateY(-10px);
  }  to {
    opacity: 1;    transform: translateY(0);
  }}
/* Gallery Styles */
.gallery-grid {  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));  gap: 1.5rem;
  margin-top: 1.5rem;}
.gallery-item {
  position: relative;  border-radius: var(--border-radius);
  overflow: hidden;  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all var(--transition-speed) ease;  background-color: white;
}
.gallery-item:hover {  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);}
.gallery-item img {
  width: 100%;  height: 180px;
  object-fit: cover;  display: block;
  transition: transform 0.5s ease;}
.gallery-item:hover img {
  transform: scale(1.05);}
.gallery-caption {
  padding: 1rem;  background-color: white;
  font-size: 0.9rem;  border-top: 1px solid rgba(0, 0, 0, 0.05);
  color: var(--text-medium);}
.gallery-actions {
  position: absolute;  top: 0.75rem;
  right: 0.75rem;  z-index: 10;
  opacity: 0;  transition: opacity 0.3s ease;
}
.gallery-item:hover .gallery-actions {  opacity: 1;
}
.delete-btn {  background-color: rgba(244, 67, 54, 0.9);
  color: white;  border: none;
  border-radius: 50%;  width: 36px;
  height: 36px;  display: flex;
  align-items: center;  justify-content: center;
  cursor: pointer;  transition: all 0.2s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);}
.delete-btn:hover {
  background-color: var(--danger);  transform: scale(1.1);
}
/* File Upload Styles */input[type="file"] {
  width: 0.1px;  height: 0.1px;
  opacity: 0;  overflow: hidden;
  position: absolute;  z-index: -1;
}
input[type="file"] + label {  display: inline-block;
  padding: 0.75rem 1.5rem;  background-color: var(--neutral-light);
  color: var(--text-dark);  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);  cursor: pointer;
  transition: all var(--transition-speed) ease;  font-weight: 500;
  display: flex;  align-items: center;
  gap: 0.5rem;  margin-top: 0.5rem;
}
input[type="file"] + label:hover {  background-color: var(--neutral-beige);
}
input[type="file"] + label i {  font-size: 1.1rem;
}
#image-preview {  margin: 1.5rem 0;
  display: flex;  justify-content: center;
}
#image-preview img {  max-width: 100%;
  max-height: 300px;  border-radius: var(--border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);  transition: all var(--transition-speed) ease;
}
/* Horizontal Rule */hr {
  margin: 2rem 0;  border: none;
  height: 1px;  background-color: var(--neutral-beige);
}
/* Section Headings */h3 {
  color: var(--text-dark);  font-size: 1.2rem;
  margin-bottom: 1rem;  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--neutral-beige);}
/* Empty State */
.empty-state {  text-align: center;
  padding: 2rem;  color: var(--text-medium);
}
.empty-state i {  font-size: 3rem;
  margin-bottom: 1rem;  color: var(--neutral-beige);
}
/* Responsive Styles */@media (max-width: 992px) {
  .gallery-grid {    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }}
@media (max-width: 768px) {
  .content-header {    flex-direction: column;
    align-items: flex-start;    gap: 1rem;
  }  
  .content-header .btn {    align-self: flex-start;
  }  
  .tabs {    overflow-x: auto;
    padding-bottom: 5px;  }
    .tab-btn {
    padding: 0.75rem 1rem;    white-space: nowrap;
  }  
  .gallery-grid {    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }  
  .gallery-item img {    height: 150px;
  }  
  .form-actions {    flex-direction: column;
  }  
  .form-actions .btn {    width: 100%;
  }}
@media (max-width: 576px) {
  .gallery-grid {    grid-template-columns: 1fr 1fr;
  }  
  .card-header {    flex-direction: column;
    align-items: flex-start;    gap: 0.5rem;
  }  
  .content-wrapper {    padding: 1rem;
  }}
/* Custom File Input Styling */
.file-upload-container {  position: relative;
  margin-top: 1rem;}
.file-upload-btn {
  display: inline-flex;  align-items: center;
  gap: 0.5rem;  padding: 0.75rem 1.25rem;
  background-color: var(--neutral-light);  color: var(--text-dark);
  border: 1px dashed var(--neutral-beige);  border-radius: var(--border-radius);
  cursor: pointer;  transition: all var(--transition-speed) ease;
  width: 100%;  justify-content: center;
}
.file-upload-btn:hover {  background-color: var(--neutral-beige);
  border-color: var(--primary-green);}
.file-upload-btn i {
  font-size: 1.2rem;  color: var(--primary-green);
}
.file-name {  margin-top: 0.5rem;
  font-size: 0.9rem;  color: var(--text-medium);
}
/* Enhanced Image Preview */.preview-container {
  margin-top: 1.5rem;  background-color: var(--neutral-light);
  border-radius: var(--border-radius);  padding: 1rem;
  text-align: center;}
.preview-container img {
  max-width: 100%;  max-height: 300px;
  border-radius: calc(var(--border-radius) - 4px);  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
.preview-placeholder {  padding: 3rem 1rem;
  color: var(--text-medium);  border: 2px dashed var(--neutral-beige);
  border-radius: var(--border-radius);}
.preview-placeholder i {
  font-size: 2rem;  margin-bottom: 1rem;
  color: var(--neutral-beige);}
/* Animation for Tab Content */
.tab-content {  display: none;
  animation: fadeIn 0.3s ease-in-out;}
.tab-content.active {
  display: block;}
@keyframes fadeIn {
  from {    opacity: 0;
    transform: translateY(10px);  }
  to {    opacity: 1;
    transform: translateY(0);
  }
}

















































































































































































































































































