@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
        /* Global Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Poppins", sans-serif;
        }
        
        :root {
            --primary-green: #2a4858;
            --primary-brown: #8B7355;
            --accent-sage: #2a4858ac;
            --accent-terracotta: #967259;
            --accent-light-brown: #A68C69;
            --neutral-cream: #F2E8DC;
            --neutral-beige: #D8C3A5;
            --neutral-light: #F6F4F0;
            --neutral-dark: #3A3026;
            --text-dark: #3A3026;
            --text-medium: #5D4E41;
            --text-light: #F6F4F0;
        }

        body {
            line-height: 1.6;
            color: var(--text-dark);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 15px;
        }
        
        h1, h2, h3, h4, h5 {
            font-weight: 500;
            color: var(--primary-green);
        }
        
        /* Header Banner */
        .hero-banner {
            position: relative;
            height: 400px;
            background-image: url('https://images.unsplash.com/photo-1526392060635-9d6019884377?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: white;
            margin-bottom: 40px;
        }
        
        .hero-banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: var(--accent-sage);
        }
        
        .hero-banner-content {
            position: relative;
            z-index: 1;
        }
        
        .hero-banner h1 {
            font-size: 42px;
            font-weight: bold;
            color: white;
            margin-bottom: 20px;
            max-width: 800px;
        }
        
        .secondary-btn {
            display: inline-block;
            padding: 8px 15px;
            background-color: transparent;
            border: 2px solid white;
            color: white;
            text-decoration: none;
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .secondary-btn:hover {
            background-color: var(--text-light);
            color: var(--primary-green);
            transform: translateY(-2px);
        }
        
        .secondary-btn:active {
            transform: translateY(0);
        }
        
        /* Tour Cards Section */
        .tour-cards-section {
            background-color: var(--neutral-light);
            padding: 40px 0;
            margin-bottom: 40px;
        }
        
        .tour-cards-section h2 {
            text-align: center;
            font-size: 38px;
            margin-bottom: 30px;

            font-weight: bold;
            text-transform: uppercase;
        }
        
        .cards-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin: 0 auto;
            max-width: 1000px;
            background-color: white;
            padding: 20px;
        }
        
        .tour-card {
            background-color: var(--neutral-cream);
            border: 1px solid var(--neutral-beige);
            overflow: hidden;
            margin-bottom: 20px;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        

        
        .tour-card-image {
            height: 180px;
            background-size: cover;
            background-position: center;
            position: relative;
        }
        

        

        
        .tour-tag {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: white;
            padding: 5px 10px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .tour-card-content {
            padding: 15px;
        }
        
        .tour-type {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        
        .tour-card h3 {
            font-size: 18px;
            margin-bottom: 10px;
        }
        
        .tour-days {
            font-size: 14px;
            margin-bottom: 10px;
        }
        
        .price-from {
            font-size: 12px;
            color: #666;
        }
        
        .price {
            font-size: 18px;
            font-weight: bold;
            color: var(--accent-terracotta);
            margin-bottom: 10px;
        }
        
        .tour-description {
            font-size: 14px;
            margin-bottom: 15px;
            color: var(--text-medium);
        }
        
        .primary-btn {
            display: block;
            width: 100%;
            padding: 12px;
            background-color: var(--primary-brown);
            color: var(--text-light);
            text-align: center;
            text-decoration: none;
            font-weight: bold;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .primary-btn:hover {
            background-color: var(--accent-light-brown);
            transform: translateY(-2px);
        }
        
        .primary-btn:active {
            transform: translateY(0);
        }
        
        /* Tour Levels Section */
        .tour-levels {
            max-width: 1000px;
            margin: 0 auto 60px;
            padding: 0 20px;
        }
        
        .tour-levels h2 {
            text-align: center;
            font-size: 28px;
            margin-bottom: 30px;
            font-weight: 600;
            
        }
        
        .levels-container {
            display: flex;
            justify-content: space-between;
            gap: 20px;
        }
        
        .level-card {
            flex: 1;
            background-color: var(--neutral-beige);
            padding: 20px;

        }
        

        
        .level-title {
            font-size: 16px;
            text-transform: uppercase;
            color: var(--primary-green);
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .level-description {
            font-size: 14px;
            color: var(--text-medium);
        }
        
        /* Video Section */
        .video-section {
            position: relative;
            margin-bottom: 60px;
            height: 400px;
            background-image: url('https://images.unsplash.com/photo-1590523277543-a94d2e4eb00b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1950&q=80');
            background-size: cover;
            background-position: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .play-btn {
            width: 70px;
            height: 70px;
            background-color: var(--primary-green);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .play-btn:hover {
            background-color: var(--accent-terracotta);
            transform: scale(1.1);
        }
        
        .play-btn:active {
            transform: scale(1);
        }
        
        .play-btn i {
            color: white;
            font-size: 30px;
        }
        
        /* Call to Action */
        .cta-section {
            background-color: var(--primary-green);
            color: white;
            padding: 20px 0;
            margin-bottom: 60px;
        }
        
        .cta-container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .cta-text {
            flex: 2;
        }
        
        .cta-text h3 {
            font-size: 22px;
            color: white;
            margin-bottom: 10px;
        }
        
        .cta-text p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .cta-buttons {
            flex: 1;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        .cta-btn {
            padding: 10px 15px;
            text-decoration: none;
            font-weight: bold;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .cta-primary {
            background-color: var(--accent-terracotta);
            color: var(--text-light);
        }
        
        .cta-primary:hover {
            background-color: var(--accent-light-brown);
            transform: translateY(-2px);
        }
        
        .cta-primary:active {
            transform: translateY(0);
        }
        
        .cta-secondary {
            background-color: transparent;
            border: 1px solid white;
            color: white;
        }
        
        .cta-secondary:hover {
            background-color: var(--text-light);
            color: var(--primary-green);
            border-color: var(--text-light);
        }
        
        /* Latest from Compass */
        .latest-section {
            max-width: 1000px;
            margin: 0 auto 60px;
            padding: 0 20px;
        }
        
        .latest-section h2 {
            text-align: center;
            font-size: 28px;
            margin-bottom: 30px;
        }
        
        .latest-article {
            display: flex;
            gap: 30px;
            align-items: center;
            justify-content: center;
        }
        
        .latest-content {
            flex: 1;
        }
        
        .latest-content h3 {
            font-size: 22px;
            margin-bottom: 15px;
        }
        
        .latest-content p {
            font-size: 14px;
            color: var(--text-medium);
            margin-bottom: 20px;
        }
        
        /* Begin Journey Section */
        .begin-journey {
            background-color: var(--primary-green);
            color: var(--text-light);
            padding: 40px 0;
            margin-bottom: 60px;
        }
        
        .journey-container {
            max-width: 1000px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 20px;
        }
        
        .journey-text {
            flex: 2;
        }
        
        .journey-text h3 {
            font-size: 22px;
            color: white;
            margin-bottom: 10px;
        }
        
        .journey-text p {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .journey-buttons {
            flex: 1;
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        
        /* Brochure Section */
        .brochure-section {
            max-width: 1000px;
            margin: 0 auto 60px;
            padding: 0 20px;
            display: flex;
            gap: 30px;
            align-items: center;
        }
        
        .brochure-text {
            flex: 1;
        }
        
        .brochure-text h3 {
            font-size: 22px;
            margin-bottom: 15px;
        }
        
        .brochure-text p {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .brochure-btn {
            display: inline-block;
            padding: 12px 20px;
            background-color: var(--accent-terracotta);
            color: var(--text-light);
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
        }
        
        .brochure-btn:hover {
            background-color: var(--accent-light-brown);
            transform: translateY(-2px);
        }
        
        .brochure-btn:active {
            transform: translateY(0);
        }
        
        /* Add before/after effects */
        .hero-banner::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 30%;
            background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
        }
        
        /* Focus states for accessibility */
        .primary-btn:focus,
        .secondary-btn:focus,
        .cta-btn:focus,
        .brochure-btn:focus {
            outline: 2px solid var(--accent-terracotta);
            outline-offset: 2px;
        }