/* Hero Section with Carousel */
body {
  background-color: var(--neutral-light);
  font-family: "Helvetica Neue", Arial, sans-serif;
}

p {
  text-align: justify;
}

.hero-section {
  width: 100%;
  position: relative;
  overflow: hidden;
  margin-bottom: 2rem;
}

.hero-carousel {
  position: relative;
  width: 100%;
  height: 80vh;
  max-height: 700px;
  min-height: 500px;
}

.carousel-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.carousel-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 1s ease-in-out;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.carousel-slide.active {
  opacity: 1;
  z-index: 1;
}

.carousel-slide img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.slide-content {
  color: var(--text-light);
  padding: 2rem 4rem;
  max-width: 600px;
  position: relative;
  border-radius: 4px;
  z-index: 2;
  background-color: none;
  margin-left: 5%;
  animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-content h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  line-height: 1.2;
  color: var(--text-light);
  text-shadow: 0 0 5px #2d3748, 0 0 10px #4a5568, 0 0 20px #718096;
}

.slide-content p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  font-weight: 400;
  color: var(--text-light);
  text-shadow: 0 0 5px #2d3748, 0 0 10px #4a5568, 0 0 20px #718096;
}

.hero-btn {
  display: inline-block;
  background-color: var(--primary-brown);
  color: var(--text-light);
  padding: 12px 24px;
  border-radius: 4px;
  font-weight: bold;
  text-transform: uppercase;
  font-size: 0.9rem;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.hero-btn:hover {
  background-color: var(--accent-light-brown);
  color: var(--text-light);
}

.carousel-controls {
  position: absolute;
  bottom: 30px;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  z-index: 10;
}

.carousel-arrow {
  background-color: rgba(166, 140, 105, 0.3);
  color: var(--text-light);
  border: none;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.carousel-arrow:hover {
  background-color: rgba(166, 140, 105, 0.5);
}

.carousel-indicators {
  display: flex;
  gap: 10px;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background-color: var(--text-light);
  transform: scale(1.2);
}

/* Responsive styles for hero section */
@media (max-width: 992px) {
  .hero-carousel {
    height: 70vh;
  }

  .slide-content {
    padding: 1.5rem 2.5rem;
    max-width: 500px;
  }

  .slide-content h1 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .hero-carousel {
    height: 60vh;
    min-height: 400px;
  }

  .slide-content {
    padding: 1.5rem 2rem;
    max-width: 90%;
    margin-left: 5%;
    margin-right: 5%;
  }

  .slide-content h1 {
    font-size: 1.8rem;
  }

  .slide-content p {
    font-size: 1rem;
  }
}

@media (max-width: 576px) {
  .hero-carousel {
    height: 50vh;
    min-height: 350px;
  }

  .slide-content {
    padding: 1rem 1.5rem;
  }

  .slide-content h1 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
  }

  .slide-content p {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }

  .hero-btn {
    padding: 10px 20px;
    font-size: 0.8rem;
  }

  .carousel-controls {
    bottom: 15px;
    gap: 10px;
  }

  .carousel-arrow {
    width: 35px;
    height: 35px;
  }
}

/* Tours Section Styles */
.tours-section {
  padding: 60px 0;
  background-color: var(--neutral-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

.section-header {
  text-align: center;
  margin: 0 auto 50px;
}

.section-header h1 {
  font-size: 32px;
  color: var(--primary-green);
  margin-bottom: 20px;
  font-weight: 600;
}

.section-header p {
  font-size: 16px;
  color: var(--text-medium);
  line-height: 1.6;
}

/* Tours Grid */
.tours-grid {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.tours-row {
  display: flex;
  gap: 30px;
}

.tour-col {
  flex: 1;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(58, 48, 38, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.tour-col:hover {
  transform: translateY(-5px);
}

.tours-row a.tour-col {
  text-decoration: none;
  color: inherit;
  transition: transform 0.3s ease;
}

.tours-row a.tour-col:hover {
  transform: translateY(-5px);
}

.tour-img {
  height: 320px;
  overflow: hidden;
  position: relative;
}

.tour-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.tour-img::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 1;
}

.tour-col:hover .tour-img::before {
  opacity: 1;
}

.tour-col:hover .tour-img img {
  transform: scale(1.05);
}

.tour-content {
  padding: 25px;
  background-color: var(--neutral-light);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tour-content h2 {
  font-size: 24px;
  color: var(--primary-green);
  margin-bottom: 15px;
  font-weight: 600;
}

.tour-content p {
  font-size: 15px;
  color: var(--text-medium);
  line-height: 1.6;
  margin-bottom: 20px;
  flex: 1;
}

.btn-primary {
  display: inline-block;
  background-color: var(--primary-green);
  color: var(--text-light);
  text-decoration: none;
  padding: 10px 25px;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  transition: background-color 0.3s ease;
  align-self: flex-start;
}

.btn-primary:hover {
  background-color: var(--accent-sage);
}

/* Features Row */
.features-row {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
}

/* eature Column Styles */
.feature-col {
  position: relative;
  overflow: hidden;
  height: 380px; /* Increased from 280px */
  box-shadow: 0 8px 25px rgba(58, 48, 38, 0.15);
  border-radius: 4px;
  transform: translateY(0);
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1),
    box-shadow 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-col:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 35px rgba(58, 48, 38, 0.2);
}

.feature-img {
  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.feature-img::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 40%,
    rgba(42, 72, 88, 0.8) 100%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
}

.feature-col:hover .feature-img::after {
  opacity: 1;
}

.feature-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.01);
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(0.95);
}

.feature-col:hover .feature-img img {
  transform: scale(1.08);
  filter: brightness(1);
}

.feature-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 30px;
  background: linear-gradient(
    to top,
    rgba(42, 72, 88, 0.95),
    rgba(42, 72, 88, 0.7) 60%,
    transparent
  );
  color: var(--text-light);
  transform: translateY(0);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-col:hover .feature-overlay {
  background: linear-gradient(
    to top,
    rgba(42, 72, 88, 0.98),
    rgba(42, 72, 88, 0.8) 80%,
    rgba(42, 72, 88, 0.6)
  );
  padding-bottom: 40px;
}

.feature-overlay h3 {
  font-size: 22px;
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--text-light);
  transform: translateY(0);
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.feature-overlay p {
  font-size: 15px;
  line-height: 1.6;
  opacity: 0;
  max-height: 0;
  color: var(--neutral-cream);
  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  margin: 0;
}

.feature-col:hover .feature-overlay h3 {
  transform: translateY(-5px);
}

.feature-col:hover .feature-overlay p {
  opacity: 1;
  max-height: 200px;
  margin-top: 10px;
}

/* Responsive adjustments for feature columns */
@media (max-width: 992px) {
  .feature-col {
    height: 340px;
  }

  .feature-overlay {
    padding: 25px;
  }

  .feature-overlay h3 {
    font-size: 20px;
  }
}

@media (max-width: 768px) {
  .feature-col {
    height: 300px;
  }

  .feature-overlay {
    padding: 20px;
  }

  .feature-overlay h3 {
    font-size: 18px;
  }

  .feature-overlay p {
    font-size: 14px;
  }
}

/* Responsive Styles */
@media (max-width: 992px) {
  .section-header h1 {
    font-size: 28px;
  }

  .tour-content h2 {
    font-size: 22px;
  }
}

@media (max-width: 768px) {
  .tours-row {
    flex-direction: column;
  }

  .features-row {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .section-header {
    margin-bottom: 30px;
  }

  .section-header h1 {
    font-size: 24px;
  }

  .tour-img {
    height: 200px;
  }

  .tour-content {
    padding: 20px;
  }

  .tour-content h2 {
    font-size: 20px;
  }

  .tour-content p {
    font-size: 1rem;
  }

  .feature-col {
    height: 300px;
  }

  .feature-overlay {
    padding: 20px;
  }

  .feature-overlay h3 {
    font-size: 18px;
  }

  .feature-overlay p {
    font-size: 14px;
  }
}

/* Destinations Section */
.destinations-section {
  padding: 60px 0;
  background-color: var(--neutral-light);
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
}

.section-title h2 {
  font-size: 32px;
  color: var(--primary-green);
  font-weight: 600;
  position: relative;
  display: inline-block;
}

.main-destinations-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-template-rows: repeat(2, 200px);
  gap: 2px;
  margin-bottom: 5px;
}

.destination-item {
  position: relative;
  overflow: hidden;
  cursor: pointer;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.destination-item img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.destination-item::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.3);
  transition: background 0.4s ease;
  z-index: 1;
}

.destination-item:hover::before {
  background: rgba(0, 0, 0, 0.1);
}

.destination-item:hover img {
  transform: scale(1.03);
}

.destination-overlay {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 20px;
  transition: transform 0.4s ease;
}

.destination-item:hover .destination-overlay {
  transform: scale(1.1);
}

.destination-overlay h3 {
  color: var(--text-light);
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
  position: relative;
}

.destination-overlay h3::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 2px;
  background-color: var(--text-light);
  transition: width 0.3s ease;
}

.destination-item:hover .destination-overlay h3::after {
  width: 50px;
}

/* Enquiry Banner */
.enquiry-banner {
  background: linear-gradient(rgba(58, 48, 38, 0.7), rgba(58, 48, 38, 0.7)),
    url("../images/hero/hejuru.jpeg") center/cover;
  color: var(--text-light);
  padding: 30px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 20px;
}

.enquiry-banner h3 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  color: var(--text-light);
}

.enquire-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  background-color: var(--accent-terracotta);
  color: var(--text-light);
  text-decoration: none;
  padding: 12px 30px;
  font-size: 14px;
  font-weight: 600;
  border-radius: 4px;
  transition: background-color 0.3s ease, transform 0.2s ease;
}

.enquire-btn:hover {
  background-color: var(--accent-light-brown);
  transform: translateY(-2px);
}

.enquire-btn i {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.enquire-btn:hover i {
  transform: translateX(4px);
}

/* Popular Tours Section */
.popular-tours-section {
  padding: 60px 0;
  background-color: var(--neutral-light);
}

.title-underline {
  width: 80px;
  height: 3px;
  background-color: var(--accent-terracotta);
  margin: 15px auto 40px;
}

.tours-cards {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30px;
}

.tour-card {
  background-color: var(--neutral-light);
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(58, 48, 38, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.tour-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(58, 48, 38, 0.15);
}

.tour-card-image {
  position: relative;
  height: 250px;
  overflow: hidden;
}

.tour-card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.tour-card:hover .tour-card-image img {
  transform: scale(1.05);
}

.tour-card-image::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(58, 48, 38, 0.1),
    rgba(58, 48, 38, 0.3)
  );
  opacity: 0;
  transition: opacity 0.5s ease;
  z-index: 1;
}

.tour-card:hover .tour-card-image::before {
  opacity: 1;
}

.tour-badge {
  position: absolute;
  top: 10px;
  left: 0;
  background-color: var(--neutral-light);
  color: var(--primary-green);
  font-size: 12px;
  font-weight: 600;
  padding: 5px 10px;
  z-index: 2;
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.tour-card:hover .tour-badge {
  background-color: var(--primary-green);
  color: var(--text-light);
  transform: translateX(5px);
}

.tour-offer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(150, 114, 89, 0.9);
  color: var(--text-light);
  font-size: 12px;
  font-weight: 600;
  padding: 8px;
  text-align: center;
  z-index: 2;
  transform: translateY(0);
  transition: transform 0.3s ease, background-color 0.3s ease;
}

.tour-card:hover .tour-offer {
  background-color: var(--accent-terracotta);
  transform: translateY(-5px);
}

.tour-card-content {
  padding: 20px;
}

.tour-tags {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.tour-tag {
  font-size: 11px;
  color: var(--primary-green);
  font-weight: 600;
}

.tour-tag:not(:last-child) {
  border-right: 1px solid var(--neutral-beige);
  padding-right: 10px;
}

.tour-card-content h3 {
  font-size: 18px;
  color: var(--primary-green);
  margin: 0 0 10px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.tour-card:hover .tour-card-content h3 {
  color: var(--accent-terracotta);
}

.tour-duration {
  font-size: 12px;
  color: var(--text-medium);
  margin-bottom: 10px;
}

.tour-price {
  font-size: 14px;
  color: var (--primary-green);
  font-weight: 600;
  margin-bottom: 15px;
}

.tour-card-content p {
  font-size: 14px;
  color: var(--text-medium);
  margin-bottom: 20px;
  line-height: 1.5;
}

.read-more-btn {
  display: inline-block;
  background-color: var(--primary-green);
  color: var(--text-light);
  text-decoration: none;
  padding: 10px 20px;
  font-size: 12px;
  font-weight: 600;
  transition: background-color 0.3s ease, transform 0.3s ease;
  position: relative;
  overflow: hidden;
}

.read-more-btn::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  transition: transform 0.5s ease;
  transform: skewX(-15deg);
}

.read-more-btn:hover {
  background-color: var(--accent-sage);
  transform: translateY(-3px);
}

.read-more-btn:hover::before {
  transform: skewX(-15deg) translateX(200%);
}

/* Tailor-made Banner */
.tailor-made-banner {
  background: linear-gradient(rgba(58, 48, 38, 0.3), rgba(58, 48, 38, 0.3)),
    url("../images/hero/page-cover.jpg") center/cover;
  color: var(--text-light);
  padding: 50px 0;
  text-align: center;
}

.banner-content {
  text-align: left;
}

.banner-content h2 {
  font-size: 28px;
  font-weight: 600;
  margin: 0 0 15px;
  color: var(--text-light);
}

.banner-content p {
  font-size: 16px;
  margin: 0 0 25px;
  max-width: 800px;
  line-height: 1.6;
  color: var(--text-light);
}

.banner-actions {
  display: flex;
  align-items: center;
  gap: 30px;
}

.call-us {
  display: flex;
  align-items: center;
  gap: 10px;
}

.call-us span {
  font-size: 14px;
  font-weight: 600;
  color: var(--text-light);
}

.phone-number {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-light);
  text-decoration: none;
  font-size: 16px;
  font-weight: 600;
  transition: color 0.3s ease;
}

.phone-number:hover {
  color: var(--neutral-cream);
}

.phone-number i {
  font-size: 14px;
}

.middle-about-section {
  padding: 1rem 0;
  background: var(--neutral-light);
  position: relative;
  overflow: hidden;
}

.middle-about-section::before {
  content: "";
  position: absolute;
  width: 300px;
  height: 300px;
  border-radius: 50%;
  background: rgba(66, 133, 244, 0.05);
  top: -100px;
  left: -100px;
  z-index: 0;
}

.middle-about-section::after {
  content: "";
  position: absolute;
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: rgba(234, 67, 53, 0.05);
  bottom: -50px;
  right: -50px;
  z-index: 0;
}

.section-heading {
  text-align: center;
  margin-bottom: 1.5rem;
  position: relative;
  z-index: 1;
}

.section-heading h2 {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-heading h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 70px;
  height: 3px;
  background: var(--primary-brown);
  border-radius: 3px;
}

.section-heading p {
  font-size: 1.1rem;
  color: #718096;
  max-width: 600px;
  margin: 0 auto;
}

.video-flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  align-items: center;
  position: relative;
  z-index: 1;
}

.video-container {
  flex: 1;
}

.video-wrapper {
  overflow: hidden;
  border-radius: 4px;
  width: 100%;
}

.video-wrapper iframe {
  width: 100%;
  height: 300px;
}

.video-content-container {
  flex: 1;
}

.content-wrapper {
  position: relative;
}

.content-header {
  margin-bottom: 1rem;
}

.content-header h2 {
  font-size: 2.2rem;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 15px;
}

.content-body p {
  margin-bottom: 25px;
  font-size: 1.05rem;
  color: #4a5568;
  line-height: 1.8;
}

.content-body .highlight {
  color: #2d3748;
  font-weight: 500;
}

.cta-button i {
  margin-left: 8px;
}

@media (max-width: 992px) {
  .about-section {
    padding: 80px 0;
  }

  .section-heading h2 {
    font-size: 2.2rem;
  }
}

@media (max-width: 768px) {
  .about-section {
    padding: 60px 0;
  }

  .video-flex-container {
    flex-direction: column;
    gap: 40px;
  }

  .video-container,
  .video-content-container {
    width: 100%;
  }

  .section-heading h2 {
    font-size: 2rem;
  }

  .content-header h2 {
    font-size: 1.8rem;
  }
}

/* Animations */
.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
}

.fade-in-left {
  opacity: 0;
  transform: translateX(-30px);
}

.fade-in-right {
  opacity: 0;
  transform: translateX(30px);
}

.fade-in {
  opacity: 0;
}

.partners-section {
  max-width: 1200px;
  margin: 50px auto;
  padding: 20px;
}

.section-title {
  text-align: center;
  margin-bottom: 40px;
  font-size: 2rem;
  color: var(--primary-green);
}

.partners-slider {
  position: relative;
  overflow: hidden;
  padding: 20px 0;
}

.slider-container {
  display: flex;
  transition: transform 0.5s ease;
  align-items: center;
}

.slider-item {
  flex: 0 0 20%;
  min-width: 200px;
  padding: 0 20px;
  text-align: center;
  transition: transform 0.3s ease;
}

.slider-item:hover {
  transform: scale(1.05);
}

.slider-item img {
  max-width: 100%;
  height: auto;
  max-height: 80px;
  object-fit: contain;
}

.slider-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background: rgba(58, 48, 38, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  z-index: 10;
  border: none;
  font-size: 18px;
}

.slider-prev {
  left: 10px;
}

.slider-next {
  right: 10px;
}

@media (max-width: 768px) {
  .slider-item {
    flex: 0 0 50%;
    min-width: 150px;
  }
}

@media (max-width: 480px) {
  .slider-item {
    flex: 0 0 100%;
    min-width: 100%;
  }
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .main-destinations-grid {
    grid-template-columns: repeat(2, 1fr);
    grid-template-rows: repeat(4, 180px);
  }

  .tours-cards {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .main-destinations-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(8, 150px);
  }

  .tours-cards {
    grid-template-columns: 1fr;
  }

  .banner-actions {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }

  .enquiry-banner {
    padding: 20px;
  }

  .enquiry-banner h3 {
    font-size: 20px;
  }
}
/*  about us  */

/* filter-section */
.filter-container {
  padding: 30px 0;
  max-width: 1200px;
  margin: 0 auto 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.filter-categories {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-bottom: 25px;
  padding: 0 20px;
}

.filter-btn {
  background-color: var(--neutral-light);
  color: var(--text-dark);
  padding: 10px 18px;
  border-radius: 10px;
  font-size: 13px;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  text-transform: uppercase;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid var(--neutral-beige);
}

.filter-btn:hover {
  background-color: var(--neutral-cream);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.filter-btn.active {
  background-color: var(--accent-terracotta);
  color: var(--text-light);
}

.sorting-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
  padding: 0 20px;
}

.sort-label {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 15px;
}

.dropdown-container {
  position: relative;
}

.sort-dropdown {
  padding: 10px 16px;
  border: 1px solid var(--neutral-beige);
  border-radius: 30px;
  background-color: var(--neutral-light);
  color: var(--text-dark);
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  outline: none;
  font-family: inherit;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 40px;
}

.sort-dropdown:hover,
.sort-dropdown:focus {
  border-color: var(--accent-terracotta);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments for filters */
@media (max-width: 768px) {
  .filter-container {
    padding: 20px 10px;
  }

  .filter-categories {
    gap: 8px;
  }

  .filter-btn {
    padding: 8px 14px;
    font-size: 12px;
  }

  .sorting-controls {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: 0 15px;
  }

  .dropdown-container {
    width: 100%;
  }

  .sort-dropdown {
    width: 100%;
  }
}

.view-more-container {
  text-align: center;
  margin: 30px 0;
}

.view-more-btn {
  background-color: var(--text-dark);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.view-more-btn:hover {
  background-color: var(--text-dark);
}

.values-section {
  background-color: var(--neutral-light);
  padding: 80px 20px;
}

.section-header {
  text-align: center;
  margin-bottom: 60px;
  color: var(--primary-green);
}

.section-header h2 {
  font-size: 2.5rem;
  margin-bottom: 15px;
  position: relative;
  display: inline-block;
}

.section-header h2::after {
  content: "";
  position: absolute;
  width: 60px;
  height: 3px;
  background-color: var(--accent-terracotta);
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
}

.values-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.value-card {
  flex: 1;
  min-width: 280px;
  max-width: 500px;
  background-color: var(--neutral-cream);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.value-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 20px;
  text-align: center;
}

.card-header i {
  font-size: 2.5rem;
  margin-bottom: 15px;
  color: var(--neutral-cream);
}

.card-header h3 {
  font-size: 1.8rem;
  margin-bottom: 5px;
}

.card-body {
  padding: 30px;
  color: var(--text-medium);
  line-height: 1.6;
  text-align: center;
}

.card-body p {
  font-size: 1rem;
  font-weight: lighter;
}

@media screen and (max-width: 768px) {
  .values-container {
    flex-direction: column;
    align-items: center;
  }

  .value-card {
    width: 100%;
  }

  .section-header h2 {
    font-size: 2rem;
  }
}

.destination-container {
  display: flex;
  min-height: 80vh;
  position: relative;
  overflow: hidden;
}

.destination-content {
  flex: 1;
  padding: 60px 40px;
  position: relative;
}

.category {
  text-transform: uppercase;
  letter-spacing: 2px;
  font-size: 14px;
  margin-bottom: 40px;
  color: var(--accent-terracotta);
}

.title {
  font-size: 56px;
  margin-bottom: 30px;
  font-weight: 500;
  color: var(--primary-brown);
}

.subtitle {
  font-size: 18px;
  max-width: 600px;
  line-height: 1.6;
  margin-bottom: 30px;
  font-weight: 400;
  color: var(--text-medium);
}

.description {
  max-width: 550px;
  line-height: 1.8;
  margin-bottom: 40px;
  color: var(--text-medium);
}

.cta-button {
  display: inline-flex;
  align-items: center;
  background: transparent;
  color: var(--primary-green);
  padding: 15px 25px;
  border: 1px solid var(--accent-light-brown);
  border-radius: 50px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s;
  margin-top: 20px;
}

.cta-button:hover {
  background-color: var(--accent-light-brown);
  color: var(--neutral-light);
}

.cta-button .icon {
  margin-right: 15px;
  border: 1px solid var(--accent-light-brown);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.destination-image {
  flex: 0 0 40%;
  position: relative;
  overflow: hidden;
}

.destination-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.location-tag {
  position: absolute;
  bottom: 40px;
  right: 40px;
  transform: rotate(-90deg);
  transform-origin: bottom right;
  text-transform: uppercase;
  letter-spacing: 5px;
  font-size: 14px;
  color: var(--neutral-light);
}

@media screen and (max-width: 992px) {
  .destination-container {
    flex-direction: column;
  }

  .destination-content {
    padding: 40px 30px;
  }

  .destination-image {
    flex: 0 0 350px;
  }

  .title {
    font-size: 42px;
  }
}

@media screen and (max-width: 576px) {
  .destination-content {
    padding: 30px 20px;
  }

  .category {
    margin-bottom: 20px;
  }

  .title {
    font-size: 36px;
    margin-bottom: 20px;
  }

  .subtitle {
    font-size: 16px;
  }

  .destination-image {
    flex: 0 0 300px;
  }

  .location-tag {
    bottom: 20px;
    right: 20px;
  }
}

.destination-container {
  display: flex;
  align-items: center;
  padding: 4rem 8%;
  gap: 4rem;
  background: linear-gradient(
    to right,
    var(--neutral-light),
    var(--neutral-beige)
  );
  position: relative;
  overflow: hidden;
}

.destination-content {
  flex: 1;
  max-width: 600px;
}

.destination-content .category {
  font-size: 0.9rem;
  color: var(--accent-terracotta);
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 1rem;
  font-weight: 600;
}

.destination-content .title {
  font-size: 2.5rem;
  color: var(--primary-brown);
  margin-bottom: 1rem;
  line-height: 1.2;
  font-weight: 700;
}

.destination-content .subtitle {
  font-size: 1.2rem;
  color: var(--text-medium);
  margin-bottom: 1.5rem;
  line-height: 1.6;
  font-style: italic;
}

.destination-content .description {
  color: var(--text-medium);
  line-height: 1.8;
  margin-bottom: 2rem;
}

.destination-content .d-cta-button {
  display: inline-flex;
  align-items: center;
  padding: 1rem 2rem;
  background-color: var(--primary-green);
  color: var(--text-light);
  border: none;
  border-radius: 4px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.destination-content .d-cta-button:hover {
  background-color: var(--accent-sage);
  transform: translateY(-2px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.destination-content .d-cta-button .icon {
  margin-right: 10px;
  font-size: 1.2rem;
}

.destination-image {
  flex: 1;
  position: relative;
  opacity: 0;
  transform: translateX(100px);
  transition: all 1s ease;
}

.destination-image.visible {
  opacity: 1;
  transform: translateX(0);
}

.destination-image img {
  width: 100%;
  max-width: 600px;
  height: auto;
  border-radius: 4px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  transition: transform 0.3s ease;
}

.destination-image:hover img {
  transform: scale(1.02);
}

.destination-image .location-tag {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(242, 232, 220, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  font-weight: 600;
  color: var(--primary-green);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 968px) {
  .destination-container {
    flex-direction: column;
    padding: 2rem 5%;
    gap: 2rem;
  }

  .destination-content,
  .destination-image {
    max-width: 100%;
  }

  .destination-content .title {
    font-size: 2rem;
  }
}
.section-subtitle {
  text-align: justify;
  display: flex;
  font-weight: lighter;
  font-size: smaller;
  padding: 0 3em;
  color: var(--text-dark);
}
.sub-padd {
  position: relative;
  top: -4em;
}
.sub-margin {
  margin: 3em 0;
}
.dest-contain {
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--primary-green);
  font-size: 2.5rem;
  position: relative;
  padding-bottom: 1rem;
}

.title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background-color: var(--accent-terracotta);
}

.travel-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
}

@media (max-width: 1024px) {
  .travel-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .travel-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .travel-grid {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    gap: 1rem;
    padding-bottom: 1.5rem;
    scroll-behavior: smooth;
  }

  .month-card {
    flex: 0 0 85%;
    scroll-snap-align: center;
  }
}

.month-card {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 250px;
}

.month-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.month-card a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: var(--text-light);
}

.month-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.month-card:hover img {
  transform: scale(1.1);
}

.month-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  font-size: 1.5rem;
  font-weight: 600;
  transition: padding 0.3s ease;
}

.month-card:hover .month-name {
  padding-bottom: 1.5rem;
}

.navigation {
  display: none;
}

@media (max-width: 576px) {
  .navigation {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 1rem;
  }

  .nav-btn {
    background-color: var(--primary-green);
    color: var(--text-light);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
  }

  .nav-btn:hover {
    background-color: var(--accent-terracotta);
  }
}

.month-indicator {
  display: none;
}

@media (max-width: 576px) {
  .month-indicator {
    display: block;
    text-align: center;
    margin-top: 1rem;
    font-weight: 500;
    color: var(--text-medium);
  }
}

.dest-contain {
  max-width: 1200px;
  margin: 0 auto;
}

.title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--primary-green);
  font-size: 2.5rem;
  position: relative;
  padding-bottom: 1rem;
}

.title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background-color: var(--accent-terracotta);
}

.travel-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

@media (max-width: 1024px) {
  .travel-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .travel-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 576px) {
  .travel-grid {
    display: flex;
    overflow-x: auto;
    scroll-snap-type: x mandatory;
    gap: 1rem;
    padding-bottom: 1.5rem;
    scroll-behavior: smooth;
  }

  .month-card {
    flex: 0 0 85%;
    scroll-snap-align: center;
  }
}

.month-card {
  position: relative;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  height: 250px;
}

.month-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.month-card a {
  display: block;
  width: 100%;
  height: 100%;
  text-decoration: none;
  color: var(--text-light);
}

.month-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.month-card:hover img {
  transform: scale(1.1);
}

.month-name {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 1rem;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  font-size: 1.5rem;
  font-weight: 600;
  transition: padding 0.3s ease;
}

.month-card:hover .month-name {
  padding-bottom: 1.5rem;
}

.navigation {
  display: none;
}

@media (max-width: 576px) {
  .navigation {
    display: flex;
    justify-content: center;
    margin-top: 1.5rem;
    gap: 1rem;
  }

  .nav-btn {
    background-color: var(--primary-green);
    color: var(--text-light);
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transition: background-color 0.3s ease;
  }

  .nav-btn:hover {
    background-color: var(--accent-terracotta);
  }
}

.month-indicator {
  display: none;
}

@media (max-width: 576px) {
  .month-indicator {
    display: block;
    text-align: center;
    margin-top: 1rem;
    font-weight: 500;
    color: var(--text-medium);
  }
}

#subscriptionMessage {
  margin-top: 10px;
  padding: 10px;
  display: none;
}
#subscriptionMessage.success {
  color: #155724;
  background-color: #d4edda;
  border: 1px solid #c3e6cb;
}
#subscriptionMessage.error {
  color: #721c24;
  background-color: #f8d7da;
  border: 1px solid #f5c6cb;
}

.cta-social-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 15px;
}

.cta-social-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  border-radius: 5px;
  color: #fff;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
}

.cta-social-button i {
  font-size: 1.2em;
}

.cta-social-button.instagram {
  background: linear-gradient(
    45deg,
    #f09433 0%,
    #e6683c 25%,
    #dc2743 50%,
    #cc2366 75%,
    #bc1888 100%
  );
}

.cta-social-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
  .cta-social-buttons {
    flex-direction: column;
    width: 100%;
  }

  .cta-social-button {
    width: 100%;
    justify-content: center;
  }
}

/* Last Hero Section Styles */
.last-her {
  width: 100%;
  overflow: hidden;
}

.last-her-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.top-image {
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
}

.top-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  background-repeat: no-repeat;
  border-radius: 10px;
  transform: scale(1.01);
  transition: transform 0.7s cubic-bezier(0.4, 0, 0.2, 1);
  filter: brightness(0.95);
}

.top-image::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    transparent 50%,
    rgba(42, 72, 88, 0.7) 100%
  );
  opacity: 0;
  transition: opacity 0.5s ease;
}

.top-image:hover::after {
  opacity: 1;
}

.top-image:hover img {
  transform: scale(1.05);
  filter: brightness(1);
}

.top-image .de-label {
  position: absolute;
  bottom: 40px;
  left: 40px;
  background:transparent;
  padding: 15px 30px;
  border-radius: 4px;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-light);
  transform: translateY(0);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  z-index: 2;
}



/* Responsive styles for last hero */
@media (max-width: 1200px) {
  .top-image {
    height: 90vh;
  }
}

@media (max-width: 992px) {
  .top-image {
    height: 80vh;
  }

  .top-image .de-label {
    bottom: 30px;
    left: 30px;
    padding: 12px 24px;
    font-size: 18px;
  }
}

@media (max-width: 768px) {
  .top-image {
    height: 70vh;
  }

  .top-image .de-label {
    bottom: 25px;
    left: 25px;
    padding: 10px 20px;
    font-size: 16px;
  }
}

@media (max-width: 576px) {
  .top-image {
    height: 60vh;
  }

  .top-image .de-label {
    bottom: 20px;
    left: 20px;
    padding: 8px 16px;
    font-size: 14px;
  }
}
