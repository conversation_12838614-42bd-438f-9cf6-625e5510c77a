* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Helvetica Neue", Arial, sans-serif;
}

body {
  max-width: 100%;
  overflow-x: hidden;
  background-color: var(--neutral-light);
  color: var(--text-dark);
}

.hero-banner {
  width: 100%;
  height: 60vh;
  background-image: linear-gradient(
      rgba(42, 72, 88, 0.5),
      rgba(42, 72, 88, 0.5)
    ),
    url("../images/hero/about.jpg");
  background-size: cover;
  background-position: center;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: var(--text-light);
  text-align: center;
  position: relative;
}

.hero-title {
  font-size: 48px;
  font-weight: bold;
  margin-bottom: 20px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.rating-box {
  background-color: var(--neutral-cream);
  border-radius: 8px;
  padding: 8px 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 10px;
}

.rating-row {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 5px;
}

.rating-icons {
  display: flex;
  gap: 5px;
}

.rating-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
}

.icon-green {
  background-color: var(--primary-green);
}

.icon-brown {
  background-color: var(--primary-brown);
}

.icon-sage {
  background-color: var(--accent-sage);
}

.rating-stars {
  color: var(--accent-terracotta);
  font-size: 18px;
}

.rating-number {
  font-size: 18px;
  font-weight: bold;
  margin-right: 5px;
}

.rating-text {
  font-size: 12px;
  color: var(--text-medium);
}

.main-content {
  padding: 40px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.intro-section {
  margin-bottom: 40px;
}

.intro-title {
  font-size: 28px;
  margin-bottom: 20px;
  font-weight: bold;
}

.intro-columns {
  display: flex;
  gap: 40px;
}

.intro-column {
  flex: 1;
}

.text-link {
  color: var(--accent-terracotta);
  text-decoration: none;
}

.text-link:hover {
  text-decoration: underline;
}

.guides-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.guide-card {
  position: relative;
  height: 200px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 20px;
}

.guide-card img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.guide-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: linear-gradient(transparent, rgba(42, 72, 88, 0.7));
  color: var(--text-light);
}

.guide-title {
  font-size: 18px;
  font-weight: bold;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

p {
  margin-bottom: 16px;
  line-height: 1.6;
}

@media (max-width: 900px) {
  .guides-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  .intro-columns {
    flex-direction: column;
    gap: 20px;
  }
}

@media (max-width: 600px) {
  .guides-grid {
    grid-template-columns: 1fr;
  }
  .hero-title {
    font-size: 36px;
  }
}
