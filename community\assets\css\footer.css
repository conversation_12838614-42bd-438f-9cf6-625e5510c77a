/* Community Footer Styles */

/* Main Footer */
.footer-main {
    background: var(--text-dark);
    color: white;
    padding: 2.5rem 0 1.5rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1.5fr;
    gap: 2rem;
}

.footer-column {
    display: flex;
    flex-direction: column;
}

/* About Column */
.about-column .footer-logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.about-column .footer-logo img {
    height: 36px;
    width: auto;
}

.about-column .logo-text h4 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin: 0;
    line-height: 1.2;
}

.about-column .logo-text span {
    color: var(--neutral-cream);
    font-size: 0.8rem;
}

.footer-description {
    color: var(--neutral-cream);
    line-height: 1.7;
    margin-bottom: 2rem;
}

.footer-stats {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
}

.footer-stats .stat-item {
    text-align: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius-sm);
    backdrop-filter: blur(10px);
}

.footer-stats .stat-number {
    display: block;
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--accent-terracotta);
    margin-bottom: 0.2rem;
}

.footer-stats .stat-label {
    font-size: 0.75rem;
    color: var(--neutral-cream);
    text-transform: uppercase;
    letter-spacing: 0.3px;
}

/* Footer Titles */
.footer-title {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    position: relative;
    padding-bottom: 0.4rem;
}

.footer-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 2px;
    background: var(--accent-terracotta);
}

.footer-subtitle {
    color: var(--neutral-cream);
    font-size: 1rem;
    font-weight: 600;
    margin: 1.5rem 0 1rem 0;
}

/* Footer Links */
.footer-links {
    list-style: none;
    margin: 0;
    padding: 0;
}

.footer-links li {
    margin-bottom: 0.75rem;
}

.footer-links a {
    color: var(--neutral-cream);
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: all 0.3s ease;
    padding: 0.25rem 0;
}

.footer-links a:hover {
    color: white;
    transform: translateX(5px);
}

.footer-links a i {
    color: var(--accent-terracotta);
    width: 16px;
    font-size: 0.9rem;
}

.footer-links.categories {
    margin-top: 1rem;
}

.footer-links.categories a {
    font-size: 0.9rem;
}

/* Contact Column */
.contact-info {
    margin-bottom: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.contact-item i {
    color: var(--accent-terracotta);
    font-size: 1.1rem;
    margin-top: 0.25rem;
    width: 20px;
}

.contact-details {
    display: flex;
    flex-direction: column;
}

.contact-label {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.contact-details a {
    color: var(--neutral-cream);
    text-decoration: none;
    transition: color 0.3s ease;
}

.contact-details a:hover {
    color: white;
}

.contact-details span {
    color: var(--neutral-cream);
    line-height: 1.5;
}

/* Involvement Actions */
.involvement-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.involvement-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.3rem;
    padding: 0.5rem;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-weight: 500;
    font-size: 0.8rem;
    transition: all 0.3s ease;
    text-align: center;
}

.involvement-btn.volunteer {
    background: var(--accent-terracotta);
    color: white;
}

.involvement-btn.donate {
    background: transparent;
    color: var(--accent-terracotta);
    border: 2px solid var(--accent-terracotta);
}

.involvement-btn.partner {
    background: transparent;
    color: white;
    border: 2px solid white;
    grid-column: 1 / -1;
}

.involvement-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.involvement-btn.volunteer:hover {
    background: var(--accent-light-brown);
}

.involvement-btn.donate:hover {
    background: var(--accent-terracotta);
    color: white;
}

.involvement-btn.partner:hover {
    background: white;
    color: var(--text-dark);
}

/* Social Media & Partners Section */
.footer-social-section {
    background: rgba(0, 0, 0, 0.3);
    padding: 3rem 0;
}

.social-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.social-links-section h4,
.partners-section h4 {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    text-align: center;
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.social-link {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.5rem 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 0.8rem;
}

.social-link:hover {
    background: var(--accent-terracotta);
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.social-link.facebook:hover { background: #1877F2; }
.social-link.instagram:hover { background: #E4405F; }
.social-link.linkedin:hover { background: #0A66C2; }
.social-link.youtube:hover { background: #FF0000; }
.social-link.whatsapp:hover { background: #25D366; }

.social-link i {
    font-size: 1.1rem;
}

.social-link span {
    font-weight: 500;
    font-size: 0.9rem;
}

/* Partners */
.partners-logos {
    display: flex;
    gap: 2rem;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
}

.partner-logo {
    height: 36px;
    width: auto;
    opacity: 0.7;
    transition: all 0.3s ease;
    filter: brightness(0) invert(1);
}

.partner-logo:hover {
    opacity: 1;
    transform: scale(1.1);
}

/* Footer Bottom */
.footer-bottom {
    background: rgba(0, 0, 0, 0.5);
    padding: 2rem 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.copyright {
    color: var(--neutral-cream);
}

.copyright p {
    margin: 0;
    line-height: 1.5;
}

.tagline {
    font-style: italic;
    opacity: 0.8;
    font-size: 0.9rem;
}

.footer-bottom-links {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.footer-link {
    color: var(--neutral-cream);
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: white;
}

.footer-link.main-site {
    background: rgba(255, 255, 255, 0.1);
    padding: 0.5rem 1rem;
    border-radius: var(--border-radius-sm);
    backdrop-filter: blur(10px);
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    width: 40px;
    height: 40px;
    background: var(--primary-green);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
    opacity: 0;
    visibility: hidden;
    transform: translateY(15px);
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background: var(--accent-terracotta);
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .footer-grid {
        grid-template-columns: 2fr 1fr 1fr;
        gap: 2rem;
    }
    
    .contact-column {
        grid-column: 1 / -1;
        margin-top: 2rem;
    }
}

@media (max-width: 992px) {
    .newsletter-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-grid {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
    
    .about-column {
        grid-column: 1 / -1;
    }
    
    .social-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .newsletter-section {
        padding: 3rem 0;
    }
    
    .newsletter-text h3 {
        font-size: 1.5rem;
    }
    
    .newsletter-form .form-group {
        flex-direction: column;
    }
    
    .footer-main {
        padding: 3rem 0 2rem;
    }
    
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .footer-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }
    
    .involvement-actions {
        grid-template-columns: 1fr;
    }
    
    .social-links {
        gap: 0.75rem;
    }
    
    .social-link span {
        display: none;
    }
    
    .partners-logos {
        gap: 1rem;
    }
    
    .partner-logo {
        height: 40px;
    }
    
    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }
    
    .footer-bottom-links {
        justify-content: center;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .newsletter-section {
        padding: 2rem 0;
    }
    
    .newsletter-form {
        padding: 1.5rem;
    }
    
    .footer-main {
        padding: 2rem 0 1rem;
    }
    
    .footer-social-section {
        padding: 2rem 0;
    }
    
    .social-links {
        gap: 0.5rem;
    }
    
    .social-link {
        padding: 0.75rem;
        min-width: 44px;
        justify-content: center;
    }
    
    .back-to-top {
        bottom: 1rem;
        right: 1rem;
        width: 45px;
        height: 45px;
    }
}
