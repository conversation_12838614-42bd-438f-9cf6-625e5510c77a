
/* Hero Image Section */
.hero {
  height: 500px;
  background-size: cover;
  background-position: center;
  position: relative;
}

.tailor-made-section {
  width: 30%;
  padding: 30px;
  border-right: 1px solid #e0e0e0;
}

.tailor-made-section h2 {
  color: var(--primary-green);
  font-size: 18px;
  margin-bottom: 30px;
  /* font-weight: bold; */
}

.tailor-made-section ul {
  list-style: none;
}

.tailor-made-section ul li {
  margin-bottom: 15px;
}

.tailor-made-section ul li a {
  color: var(--primary-green);
  text-decoration: none;
  font-size: 16px;
  display: block;
  padding: 5px 0;
  transition: color 0.3s;
}

.tailor-made-section ul li a:hover {
  color: var(--accent-terracotta);
}

.featured-section {
  width: 70%;
  padding: 30px;
}

.featured-section h2 {
  color: var(--primary-green);
  font-size: 18px;
  margin-bottom: 30px;
  /* font-weight: bold; */
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.featured-item {
  display: flex;
  flex-direction: column;
}

.featured-image {
  height: 160px;
  width: 100%;
  background-size: cover;
  background-position: center;
  margin-bottom: 15px;
}

.sri-lanka-img {
  background-image: url("/api/placeholder/400/160");
}

.central-asia-img {
  background-image: url("/api/placeholder/400/160");
}

.nile-cruise-img {
  background-image: url("/api/placeholder/400/160");
}

.featured-item h3 {
  color: var(--primary-green);
  font-size: 10px;
  margin-bottom: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.featured-item p {
  color: var(--text-dark);
  font-size: 14px;
  line-height: 1.6;
}

@media (max-width: 768px) {

  .tailor-made-section,
  .featured-section {
    width: 100%;
  }

  .featured-grid {
    grid-template-columns: 1fr;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .contact-info {
    flex-direction: column;
    align-items: flex-end;
  }

  .regions-section {
    flex-direction: column;
  }

  .sidebar,
  .destinations,
  .featured {
    width: 100%;
  }
}
.crown-icon{
  display: flex;
  align-items: center;
  justify-content: center;
}
.crown-icon img{
  width: 50px;
  height: 60px;
}
/* breadcrumb navbar */

.breadcrumb-bar {
  background-color: var(--neutral-light);
  padding: 10px 0;
  display: flex;
  justify-content: center;
  align-items: center; /* Ensure vertical alignment */
  z-index: 1;
  position: relative; /* Ensure it is not hidden by other elements */
}

.breadcrumb-bar-container {
  display: flex;
  align-items: center;
  gap: 10px;
}

.breadcrumb-bar a {
  color: var(--primary-green);
  text-decoration: none;
  font-size: 10px;
  color: #444444;
}

.breadcrumb-bar span {
  color: var(--accent-terracotta);
  font-size: 10px;
}

.breadcrumb-bar span:last-child {
  /* font-weight: bold; */
  font-size: 10px;
  color: var(--text-medium);
}

/* Hero Section Styles */
.hero {
  position: relative;
  height: 500px;
  overflow: hidden;
  height: calc(40vw - 3rem);
  margin-top: -3rem;
  overflow: hidden;
  position: relative;
}

.hero img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  aspect-ratio: 1.3;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(42, 72, 88, 0.5) 0%,
    rgba(42, 72, 88, 0.3) 50%,
    rgba(42, 72, 88, 0.1) 100%
  );
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  width: 100%;
}

.hero-content h1 {
  color: white;
  font-size: 48px;
  font-weight: bold;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

/* Hero Section Styles */


.travel-associations {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  align-items: center;
}

.travel-associations img {
  height: 35px;
  width: auto;
}

/* Footer Bottom */


.video-container {
  position: relative;
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  overflow: hidden;
  transition: height 0.5s ease;
  margin-bottom: 3em;
  place-self: center;
}

.video-player {
  width: 190vh;
  height: 100vh; /* Initial height */
  object-fit: cover;
  display: block;
  margin: 0 auto; /* Center horizontally */
}

.play-button-overlay {
  position: absolute;
  bottom: -1%; /* Attach to the bottom */
  left: 50%;
  transform: translateX(-50%);
  width: 250px;
  height: 240px;
  background-color: #001e3b;
  /* border-radius: 5px; */
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  color: white;
  text-align: center;
  transition: opacity 0.3s ease;
}

.play-icon {
  width: 60px;
  height: 60px;
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 1em;
}

.play-icon::after {
  content: "";
  display: block;
  width: 0;
  height: 0;
  border-top: 10px solid transparent;
  border-left: 20px solid white;
  border-bottom: 10px solid transparent;
  margin-left: 5px;
}

.play-text {
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 1px;
  margin-bottom: 2em;
}

.adventure-text {
  font-size: 16px;
  width: 80%;
  line-height: 1.3;
  font-weight: 800;
}
/* video section  */

.body-text_column {
  max-width: 1080px;
  width: 100%;
  margin: 0 auto;
  text-align: center;
  padding-left: 2rem;
  padding-right: 2rem;
}

.experience-section {
  padding: 0rem 0;
  margin-bottom: 2rem;
  border-radius: 5px;
}

.section-header {
  color: #001e3b;
  text-transform: uppercase;
  font-size: 0.8rem;
  font-weight: 600;
  letter-spacing: 1px;
  margin-bottom: 1.5rem;
  font-weight: bolder;
}

.section-content {
  max-width: 800px;
  margin: 0 auto;
  line-height: 1.6;
}


.experience-section p {
  margin-bottom: 1rem;
  text-align: center;
}

/* about-container-above-footer */


.about-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 3em;
}

.panel {
  flex: 1;
  padding: 30px;
}

.buttons-container {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
}

@media (max-width: 768px) {
  .container {
    flex-direction: column;
  }
}