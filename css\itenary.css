.hero-section {
  height: 80vh;
  position: relative;
  overflow: hidden;
}

.hero-image-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--primary-green);
  opacity: 0.2;
}

.hero-title {
  font-size: 4rem;
  font-weight: 300;
  line-height: 0.9;
  margin-bottom: 30px;
  max-width: 800px;
  position: relative;
  z-index: 1;
  color: var(--text-light);
}

.hero-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: var(--text-light);
  padding: 2rem;
}

.hero-subtitle {
  font-size: 1.5rem;
  margin-bottom: 2rem;
  color: var(--neutral-cream);
}

.cta-button {
  display: inline-block;
  padding: 1rem 2rem;
  background-color: var(--accent-terracotta);
  color: var(--text-light);
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.cta-button:hover {
  background-color: var(--primary-brown);
}

.itinerary-section {
  padding: 4rem 0;
  background-color: var(--neutral-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

.section-title {
  text-align: center;
  color: var(--primary-green);
  margin-bottom: 3rem;
}

.itinerary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.itinerary-card {
  background-color: var(--neutral-cream);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.itinerary-card:hover {
  transform: translateY(-5px);
}

.card-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 1.25rem;
}

.card-description {
  color: var(--text-medium);
  margin-bottom: 1rem;
  line-height: 1.6;
}

.card-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: var(--accent-light-brown);
  font-size: 0.9rem;
}

.price {
  color: var(--primary-brown);
  font-weight: bold;
}

.duration {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .hero-section {
    height: 40vh;
  }

  .hero-title {
    font-size: 2.5rem;
    padding: 0 20px;
    text-align: center;
  }

  .hero-subtitle {
    font-size: 1.25rem;
  }

  .itinerary-grid {
    grid-template-columns: 1fr;
  }
}

/* navigation-tab */
.navigation-tabs {
  display: flex;
  justify-content: center;
  margin-bottom: 2rem;
  background-color: var(--neutral-cream);
  padding: 0.5rem;
  border-radius: 8px;
}

.tab-button {
  padding: 0.75rem 1.5rem;
  border: none;
  background: none;
  color: var(--text-medium);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 4px;
}

.tab-button:hover {
  color: var(--primary-green);
}

.tab-button.active {
  background-color: var(--primary-green);
  color: var(--text-light);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Itinerary Details */
.itinerary-container {
  background-color: var(--neutral-light);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.itinerary-header {
  color: var(--primary-green);
  margin-bottom: 1.5rem;
  border-bottom: 2px solid var(--accent-sage);
  padding-bottom: 1rem;
}

.itinerary-item {
  background-color: var(--neutral-cream);
  padding: 1.5rem;
  margin-bottom: 1rem;
  border-radius: 6px;
  border-left: 4px solid var(--primary-brown);
}

.itinerary-item h3 {
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.itinerary-item p {
  color: var (--text-medium);
  line-height: 1.6;
}

.itinerary-time {
  color: var(--accent-terracotta);
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.itinerary-location {
  color: var(--accent-light-brown);
  font-style: italic;
}

.itinerary-description {
  margin-top: 1rem;
  color: var(--text-medium);
}

.itinerary-day {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 0.5rem 1rem;
  border-radius: 4px;
  display: inline-block;
  margin-bottom: 1.5rem;
}

.itinerary-notes {
  background-color: var(--neutral-beige);
  padding: 1rem;
  border-radius: 4px;
  margin-top: 1rem;
  color: var(--text-dark);
  font-size: 0.9rem;
}

.info-section {
  background-color: var(--neutral-cream);
  max-width: 1000px;
  margin: -100px auto 0;
  text-align: center;
  position: relative;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.1);
  padding-top: 20px;
  padding-right: 50px;
  padding-bottom: 20px;
  padding-left: 50px;
}

.info-title {
  font-size: 1rem;
  margin-bottom: 20px;
  color: var(--text-dark);
}

.info-subtitle {
  color: var(--text-medium);
  margin-bottom: 40px;
}

.specialist-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
}

.specialist-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  object-fit: cover;
}

.specialist-info {
  text-align: left;
}

.specialist-name {
  font-size: 0.8rem;
  color: var(--text-dark);
  margin-bottom: 5px;
}

.specialist-title {
  color: var(--primary-brown);
  text-transform: uppercase;
  font-size: 0.7rem;
  letter-spacing: 1px;
  margin-bottom: 5px;
}

.phone-number {
  display: flex;
  align-items: center;
  color: var(--text-dark);
  font-weight: bold;
  margin-left: 30px;
  font-size: 0.7rem;
}

.phone-icon {
  color: var(--primary-brown);
  margin-right: 10px;
}

.description-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 40px 20px;
  text-align: center;
}

.main-heading {
  font-size: 2.5rem;
  line-height: 1.2;
  color: var(--text-dark);
  font-weight: 600;
  margin-bottom: 30px;
}

.description-text {
  font-size: 1.05rem;
  line-height: 1.6;
  color: var(--text-medium);
  margin-bottom: 25px;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}

.tabs {
  display: flex;
  justify-content: center;
  border-bottom: 1px solid var(--neutral-cream);
  margin-bottom: 30px;
}

.tab {
  padding: 15px 10px;
  font-size: 16px;
  cursor: pointer;
  color: var(--text-medium);
  font-weight: 600;
  text-align: center;
  flex: 1;
  max-width: 300px;
  text-transform: uppercase;
  letter-spacing: 1px;
  position: relative;
}

.tab.active {
  color: var(--primary-green);
}

.tab.active::after {
  content: "";
  position: absolute;
  bottom: -2px;
  left: 15%;
  width: 70%;
  height: 3px;
  background-color: var(--primary-green);
}

.card-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.card {
  border: 1px solid var(--neutral-cream);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.card-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.card-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.card:hover .card-image img {
  transform: scale(1.05);
}

.card-badge {
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--neutral-cream);
  color: var(--text-dark);
  padding: 5px 10px;
  font-size: 12px;
  font-weight: bold;
}

.offer-badge {
  position: absolute;
  bottom: 10px;
  right: 10px;
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 8px 10px;
  font-size: 12px;
  font-weight: bold;
}

.card-tags {
  display: flex;
  border-bottom: 1px solid var(--neutral-cream);
}

.card-tag {
  position: relative;
  flex: 1;
  padding: 10px;
  text-align: center;
  font-size: 14px;
  color: var(--text-medium);
  border-right: 1px solid var(--neutral-cream);
  text-transform: uppercase;
  font-weight: 500;
}

.card-tag:last-child {
  border-right: none;
}

.card-content {
  padding: 20px;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
}

.card-title {
  font-size: 24px;
  color: var(--text-dark);
  margin-bottom: 15px;
  text-align: center;
}

.card-duration {
  text-align: center;
  margin-bottom: 15px;
  font-size: 14px;
  font-weight: 600;
}

.card-price {
  text-align: center;
  color: var(--primary-brown);
  font-weight: bold;
  margin-bottom: 15px;
  font-size: 14px;
}

.card-description {
  text-align: center;
  color: var(--text-medium);
  margin-bottom: 20px;
  font-size: 14px;
  line-height: 1.5;
  flex-grow: 1;
}

.card-button {
  background-color: var(--primary-brown);
  color: var(--text-light);
  border: none;
  padding: 12px;
  text-align: center;
  text-transform: uppercase;
  font-weight: 600;
  cursor: pointer;
  width: 100%;
  letter-spacing: 1px;
  transition: background-color 0.3s ease;
}

.card-button:hover {
  background-color: var(--accent-terracotta);
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

@media (max-width: 992px) {
  .card-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .card-grid {
    grid-template-columns: 1fr;
  }
}
/* filter-section */
.filter-container {
  padding: 30px 0;
  max-width: 1200px;
  margin: 0 auto 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.filter-categories {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 12px;
  margin-bottom: 25px;
  padding: 0 20px;
}

.filter-btn {
  background-color: #f7f7f7;
  color: #333;
  padding: 10px 18px;
  border-radius: 10px;
  font-size: 13px;
  text-decoration: none;
  transition: all 0.3s ease;
  font-weight: 500;
  text-transform: uppercase;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  border: 1px solid transparent;
  cursor: pointer;
  padding: 8px 16px;
  margin: 0 4px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background: #fff;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background-color: var(--neutral-cream);
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
}

.filter-btn.active {
  background-color: var(--primary-green);
  border-color: var(--primary-green);
  color: white;
}

.sorting-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  gap: 15px;
  margin-top: 10px;
  padding: 0 20px;
}

.sort-label {
  font-weight: 600;
  color: #333;
  font-size: 15px;
}

.dropdown-container {
  position: relative;
}

.sort-dropdown {
  padding: 10px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 30px;
  background-color: #ffffff;
  color: #333;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 160px;
  outline: none;
  font-family: inherit;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 10px center;
  background-size: 16px;
  padding-right: 40px;
}

.sort-dropdown:hover,
.sort-dropdown:focus {
  border-color: var(--accent-terracotta);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments for filters */
@media (max-width: 768px) {
  .filter-container {
    padding: 20px 10px;
  }

  .filter-categories {
    gap: 8px;
  }

  .filter-btn {
    padding: 8px 14px;
    font-size: 12px;
  }

  .sorting-controls {
    flex-direction: column;
    align-items: flex-start;
    width: 100%;
    padding: 0 15px;
  }

  .dropdown-container {
    width: 100%;
  }

  .sort-dropdown {
    width: 100%;
  }
}

.view-more-container {
  text-align: center;
  margin: 30px 0;
}

.view-more-btn {
  background-color: #4a4a4a;
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 5px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.view-more-btn:hover {
  background-color: #333;
}

.tour-card {
  display: none;
}

.tour-card.visible {
  display: block;
}

.view-more-btn {
  padding: 10px 20px;
  background-color: var(--primary-green);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  margin: 20px auto;
  display: block;
}

.view-more-btn:hover {
  background-color: var(--accent-sage);
}

