/* Privacy Management Styles */

.content-header {
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--neutral-beige);
}

.content-header h1 {
  color: var(--primary-green);
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.content-header p {
  color: var(--text-medium);
  font-size: 1.1rem;
}

/* Alert Styles */
.alert {
  padding: 1rem 1.5rem;
  border-radius: var(--border-radius);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-weight: 500;
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert a {
  color: inherit;
  text-decoration: underline;
  font-weight: 600;
}

.alert a:hover {
  opacity: 0.8;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.stat-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: center;
  gap: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-green);
  margin: 0;
}

.stat-content p {
  color: var(--text-medium);
  margin: 0;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Content Sections */
.content-section {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
  overflow: hidden;
}

.section-header {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.section-header h2 {
  margin: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

/* Policy Management */
.policy-info {
  padding: 1.5rem 2rem;
  background: var(--neutral-light);
  border-bottom: 1px solid var(--neutral-beige);
}

.policy-editor {
  padding: 2rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-green);
}

textarea.form-control {
  resize: vertical;
  min-height: 200px;
}

.form-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
}

.btn-primary {
  background: var(--primary-green);
  color: white;
}

.btn-primary:hover {
  background: var(--accent-sage);
  transform: translateY(-1px);
}

.btn-success {
  background: var(--success);
  color: white;
}

.btn-success:hover {
  background: #45a049;
  transform: translateY(-1px);
}

.btn-warning {
  background: var(--warning);
  color: var(--text-dark);
}

.btn-warning:hover {
  background: #e0a800;
  transform: translateY(-1px);
}

.btn-danger {
  background: var(--danger);
  color: white;
}

.btn-danger:hover {
  background: #da190b;
  transform: translateY(-1px);
}

.btn-secondary {
  background: var(--neutral-beige);
  color: var(--text-dark);
}

.btn-secondary:hover {
  background: var(--neutral-cream);
  transform: translateY(-1px);
}

.btn-info {
  background: var(--info);
  color: white;
}

.btn-info:hover {
  background: #0b7dda;
  transform: translateY(-1px);
}

.btn-sm {
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
}

/* Filter Controls */
.filter-controls {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.filter-controls select {
  padding: 0.5rem 1rem;
  border: 2px solid white;
  border-radius: var(--border-radius);
  background: white;
  color: var(--text-dark);
  font-size: 0.9rem;
}

/* Requests Table */
.requests-table-container {
  overflow-x: auto;
  padding: 2rem;
}

.requests-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 0.9rem;
}

.requests-table th,
.requests-table td {
  padding: 1rem;
  text-align: left;
  border-bottom: 1px solid var(--neutral-beige);
}

.requests-table th {
  background: var(--neutral-light);
  font-weight: 600;
  color: var(--text-dark);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.8rem;
}

.requests-table tbody tr:hover {
  background: var(--neutral-light);
}

/* Request Type Badges */
.request-type {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.request-type.data_access {
  background: #e3f2fd;
  color: #1565c0;
}

.request-type.data_deletion {
  background: #ffebee;
  color: #c62828;
}

.request-type.data_portability {
  background: #f3e5f5;
  color: #7b1fa2;
}

.request-type.data_correction {
  background: #fff3e0;
  color: #ef6c00;
}

/* Status Badges */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.pending {
  background: #fff3cd;
  color: #856404;
}

.status-badge.in_progress {
  background: #cce5ff;
  color: #004085;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.rejected {
  background: #f8d7da;
  color: #721c24;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.5rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  margin: 5% auto;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  padding: 1.5rem 2rem;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  font-size: 1.25rem;
}

.close {
  font-size: 1.5rem;
  font-weight: bold;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

.close:hover {
  opacity: 0.7;
}

.modal-body {
  padding: 2rem;
}

/* Request Detail Styles */
.request-detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item label {
  font-weight: 600;
  color: var(--text-dark);
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-item span {
  color: var(--text-medium);
}

.message-content,
.response-content {
  background: var(--neutral-light);
  padding: 1rem;
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-green);
  white-space: pre-wrap;
  line-height: 1.6;
}

.response-content {
  border-left-color: var(--accent-sage);
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1.5rem;
  border-top: 1px solid var(--neutral-beige);
}

/* Search and Export Controls */
.controls-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 1rem;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 300px;
}

.search-box input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 2px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  font-size: 1rem;
}

.search-box i {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-medium);
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .filter-controls {
    width: 100%;
  }

  .form-actions {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }

  .modal-content {
    margin: 10% auto;
    width: 95%;
  }

  .request-detail-grid {
    grid-template-columns: 1fr;
  }

  .controls-bar {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .modal-actions {
    flex-direction: column;
  }
}
