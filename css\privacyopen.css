/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Helvetica Neue", Arial, sans-serif;
}

body {
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--neutral-light);
}

/* Hero Section */
.hero {
  position: relative;
  height: 90vh;
  overflow: hidden;
  z-index: 1;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  position: absolute;
  top: 0;
  left: 0;
}



.hero-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: var(--text-light);
  font-size: 2.5rem;
  font-weight: bold;
  text-align: center;
  width: 90%;
  max-width: 800px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  z-index: 3;
}

/* Container */
.container {
  position: relative;
  margin-top: -2px;
  background: var(--neutral-light);
  border-radius: 15px 15px 0 0;
  padding: 30px 20px;
  z-index: 4;
}

/* Main Content */
.main-content {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20px;
}

.content {
  flex: 7;
  padding-right: 30px;
}

.sidebar-privacy {
  flex: 3;
  padding-left: 20px;
  border-left: 1px solid var(--neutral-beige);
}

/* Intro Text */
.intro-text {
  margin-bottom: 30px;
  font-size: 15px;
  color: var(--text-medium);
}

/* Section Titles */
h2 {
  margin: 25px 0 15px;
  color: var(--text-dark);
  font-size: 22px;
  font-weight: bold;
}

h3 {
  margin: 20px 0 10px;
  color: var(--text-dark);
  font-size: 18px;
  font-weight: bold;
}

/* Links */
a {
  color: var(--primary-green);
  text-decoration: none;
}

a:hover {
  text-decoration: underline;
}

/* Buttons and Highlight Elements */
.highlight {
  color: var(--primary-green);
  font-weight: bold;
}

.search-form {
  display: flex;
  margin: 15px 0;
}

/* Guide List Section */
.guide-list {
  margin-top: 20px;
  margin-bottom: 30px;
}

.guide-list h2 {
  margin-bottom: 15px;
}

.guide-list ol {
  padding-left: 35px;
  margin-bottom: 15px;
}

.guide-list li {
  margin-bottom: 5px;
  color: var(--text-medium);
}

.guide-list a {
  font-weight: bold;
}

/* About Section */
.about-section {
  background-color: var(--neutral-cream);
  padding: 20px;
  margin-bottom: 30px;
  border-radius: 5px;
}

.about-section h3 {
  color: var(--text-dark);
  margin-top: 0;
}

/* Blog Posts */
.recent-posts {
  margin-top: 30px;
}

.post-item {
  display: flex;
  margin-bottom: 15px;
  align-items: center;
}

.post-thumbnail {
  width: 70px;
  height: 70px;
  margin-right: 10px;
  object-fit: cover;
}

.post-title {
  font-size: 14px;
  line-height: 1.3;
}

/* Popular Tours */
.popular-tours {
  margin-top: 30px;
}

.tour-item {
  display: flex;
  margin-bottom: 15px;
  align-items: start;
}

.tour-thumbnail {
  width: 80px;
  height: 80px;
  margin-right: 10px;
  object-fit: cover;
}

.tour-info h4 {
  font-size: 14px;
  margin-bottom: 5px;
}

.tour-price {
  color: var(--primary-brown);
  font-size: 12px;
  font-weight: bold;
}

/* Responsive */
@media (max-width: 768px) {
  .main-content {
    flex-direction: column;
  }

  .content {
    padding-right: 0;
  }

  .sidebar-privacy {
    padding-left: 0;
    border-left: none;
    margin-top: 30px;
  }
}

/* Image at bottom of article */
.article-image {
  width: 100%;
  max-width: 600px;
  margin: 20px 0;
}

/* Lists */
ul {
  list-style-type: disc;
  padding-left: 20px;
  margin-bottom: 15px;
}

ol {
  padding-left: 20px;
  margin-bottom: 15px;
}

/* Colored list items */
.numbered-list {
  list-style-type: decimal;
  color: var(--primary-green);
  padding-left: 25px;
}

.numbered-list li {
  margin-bottom: 8px;
}

.numbered-list li span {
  color: var(--text-medium);
}

.numbered-list a {
  color: var(--primary-green);
}

/* Pagination */
.pagination {
  display: flex;
  justify-content: space-between;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid var(--neutral-beige);
}

.pagination a {
  color: var(--text-medium);
  font-size: 14px;
}
