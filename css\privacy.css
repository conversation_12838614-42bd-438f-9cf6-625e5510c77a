* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: var(--neutral-light);
  color: var(--text-medium);
  line-height: 1.6;
}

.hero {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 2rem;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.1;
  background-image: url("../images/hero/cover5.JPG");
  background-size: cover;
  background-position: center;
  z-index: 0;
}

.hero-content {
  position: relative;
  z-index: 1;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.hero p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
}

.last-updated {
  margin-top: 1rem;
  font-style: italic;
  font-size: 0.9rem;
}

 main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;

}

.toc {
  background-color: var(--neutral-cream);
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 2rem;
}

.toc h2 {
  color: var(--primary-green);
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.toc-list {
  list-style-type: none;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
}

.toc-list li {
  padding: 0.5rem 0;
}

.toc-list a {
  color: var(--primary-brown);
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.toc-list a:hover {
  color: var(--accent-terracotta);
  transform: translateX(5px);
}

.toc-icon {
  display: inline-block;
  width: 20px;
  height: 20px;
  background-color: var(--accent-light-brown);
  color: var(--text-light);
  border-radius: 50%;
  text-align: center;
  line-height: 20px;
  font-size: 0.8rem;
}

.policy-section {
  margin-bottom: 3rem;
  padding: 2rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.policy-section:hover {
  transform: translateY(-5px);
}

.policy-section h2 {
  color: var(--primary-green);
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--neutral-beige);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.section-icon {
  background-color: var(--accent-sage);
  color: var(--text-light);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.policy-section h3 {
  color: var(--accent-terracotta);
  margin: 1.5rem 0 1rem;
  font-size: 1.4rem;
}

.policy-section p,
.policy-section ul {
  margin-bottom: 1rem;
}

.policy-section ul {
  padding-left: 2rem;
}

.policy-section ul li {
  margin-bottom: 0.5rem;
}

.highlight-box {
  background-color: var(--neutral-cream);
  padding: 1.5rem;
  border-left: 4px solid var(--primary-brown);
  margin: 1.5rem 0;
  border-radius: 0 4px 4px 0;
}

/* .contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1.5rem;
  max-width: 800px;
  margin: 3rem auto;
} */

.contact-card {
  background-color: var(--neutral-cream);
  padding: 1.5rem;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  transition: all 0.3s ease;
}

.contact-card:hover {
  transform: scale(1.05);
  background-color: var(--neutral-beige);
}

.contact-icon {
  width: 50px;
  height: 50px;
  background-color: var(--primary-green);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  margin-bottom: 1rem;
}

.contact-card h4 {
  margin-bottom: 0.5rem;
  color: var(--primary-brown);
}

.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: var(--primary-brown);
  color: white;
  width: 50px;
  height: 50px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
  opacity: 0;
  pointer-events: none;
}

.back-to-top.visible {
  opacity: 1;
  pointer-events: all;
}

.back-to-top:hover {
  background-color: var(--accent-terracotta);
  transform: translateY(-5px);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .hero h1 {
    font-size: 2rem;
  }

  .hero p {
    font-size: 1rem;
  }

  .toc-list {
    grid-template-columns: 1fr;
  }

  .policy-section {
    padding: 1.5rem;
  }

  .contact-methods {
    grid-template-columns: 1fr;
  }
}

/* Animation for scroll reveal */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.scroll-reveal {
  opacity: 0;
  animation: fadeInUp 0.5s ease forwards;
}

.delay-1 {
  animation-delay: 0.1s;
}
.delay-2 {
  animation-delay: 0.2s;
}
.delay-3 {
  animation-delay: 0.3s;
}
.delay-4 {
  animation-delay: 0.4s;
}
.delay-5 {
  animation-delay: 0.5s;
}

/* Database-driven Privacy Content Styles */
.privacy-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  background-color: white;
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-lg);
  margin-bottom: var(--spacing-xl);
}

.policy-content {
  line-height: 1.8;
  color: var(--text-medium);
  font-size: 1.1rem;
}

/* Typography for database content */
.policy-content h1 {
  color: var(--primary-green);
  font-size: 3rem;
  font-weight: 700;
  margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
  text-align: center;
  border-bottom: 4px solid var(--accent-sage);
  padding-bottom: var(--spacing-md);
  position: relative;
}

.policy-content h1::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 4px;
  background: linear-gradient(90deg, var(--accent-terracotta), var(--accent-light-brown));
  border-radius: 2px;
}

.policy-content h2 {
  color: var(--primary-green);
  font-size: 2.2rem;
  font-weight: 600;
  margin: var(--spacing-xl) 0 var(--spacing-lg) 0;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) 0;
  border-bottom: 2px solid var(--neutral-beige);
  position: relative;
}

.policy-content h2::before {
  content: '';
  position: absolute;
  left: 0;
  bottom: -2px;
  width: 60px;
  height: 2px;
  background: var(--accent-terracotta);
}

.policy-content h2 .section-icon {
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  color: white;
  width: 45px;
  height: 45px;
  border-radius: var(--border-radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.3rem;
  font-weight: bold;
  box-shadow: var(--shadow-md);
  flex-shrink: 0;
}

.policy-content h3 {
  color: var(--accent-terracotta);
  font-size: 1.6rem;
  font-weight: 600;
  margin: var(--spacing-lg) 0 var(--spacing-md) 0;
  position: relative;
  padding-left: var(--spacing-md);
}

.policy-content h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  background: linear-gradient(180deg, var(--accent-terracotta), var(--accent-light-brown));
  border-radius: 2px;
}

/* Paragraph and text styling */
.policy-content p {
  margin: var(--spacing-md) 0;
  text-align: justify;
  line-height: 1.8;
  color: var(--text-medium);
}

.policy-content p:first-of-type {
  font-size: 1.2rem;
  color: var(--text-dark);
  font-weight: 500;
}

/* List styling */
.policy-content ul {
  margin: var(--spacing-md) 0;
  padding-left: var(--spacing-xl);
  position: relative;
}

.policy-content ul::before {
  content: '';
  position: absolute;
  left: var(--spacing-md);
  top: 0;
  bottom: 0;
  width: 2px;
  background: linear-gradient(180deg, var(--accent-sage), var(--neutral-beige));
  border-radius: 1px;
}

.policy-content li {
  margin: var(--spacing-sm) 0;
  line-height: 1.7;
  position: relative;
  list-style: none;
  padding-left: var(--spacing-md);
  text-align: justify;
}

.policy-content li::before {
  content: '▶';
  position: absolute;
  /* left: -var(--spacing-md); */
  color: var(--accent-terracotta);
  font-size: 0.8rem;
  top: 0.2rem;
  margin-left: -20px;
}

/* Strong text styling */
.policy-content strong {
  color: var(--primary-green);
  font-weight: 600;
  background: linear-gradient(120deg, transparent 0%, var(--neutral-cream) 0%, var(--neutral-cream) 100%, transparent 100%);
  padding: 0.1rem 0.3rem;
  border-radius: var(--border-radius-sm);
}

/* Highlight boxes for important information */
.policy-content .highlight-box,
.highlight-box {
  background: linear-gradient(135deg, var(--neutral-cream), var(--neutral-beige));
  border: 1px solid var(--accent-light-brown);
  border-left: 6px solid var(--primary-brown);
  padding: var(--spacing-lg);
  margin: var(--spacing-lg) 0;
  border-radius: var(--border-radius-md);
  position: relative;
  box-shadow: var(--shadow-sm);
}

.highlight-box::before {
  content: '💡';
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  font-size: 1.5rem;
  opacity: 0.7;
}

.highlight-box p {
  margin: 0;
  color: var(--text-dark);
  font-weight: 500;
}

.highlight-box strong {
  color: var(--primary-brown);
}

/* Privacy Contact Section */
/* .privacy-contact-section {
  background: linear-gradient(135deg, var(--neutral-light) 0%, var(--neutral-cream) 50%, var(--neutral-beige) 100%);
  padding: var(--spacing-xl) 0;
  margin-top: var(--spacing-xl);
  position: relative;
  overflow: hidden;
} */

/* .privacy-contact-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-green), var(--accent-sage), var(--accent-terracotta), var(--accent-light-brown));
} */

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--spacing-lg);
  text-align: center;
}

.contact-container h2 {
  color: var(--primary-green);
  font-size: 2.5rem;
  margin-bottom: var(--spacing-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-md);
}

.contact-container h2 i {
  color: var(--accent-terracotta);
  font-size: 2rem;
}

.contact-container > p {
  font-size: 1.2rem;
  color: var(--text-medium);
  max-width: 800px;
  margin: 0 auto var(--spacing-xl) auto;
  line-height: 1.6;
}

/* Contact Methods Grid */
.contact-methods {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin: var(--spacing-xl) 0;
  max-width: 1000px;
  margin-left: auto;
  margin-right: auto;
}


.contact-icon {
  width: 70px;
  height: 70px;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  color: white;
  border-radius: var(--border-radius-round);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  margin: 0 auto var(--spacing-md) auto;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}


/* Privacy Request CTA */
.privacy-request-cta {
  background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 50%, var(--primary-brown) 100%);
  color: white;
  padding: var(--spacing-xl);
  border-radius: var(--border-radius-lg);
  text-align: center;
  margin: var(--spacing-xl) 0;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.privacy-request-cta::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
  animation: shimmer 3s ease-in-out infinite;
}

@keyframes shimmer {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.privacy-request-cta h3 {
  color: white;
  font-size: 2.2rem;
  margin-bottom: var(--spacing-md);
  font-weight: 700;
  position: relative;
  z-index: 1;
}

.privacy-request-cta p {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.2rem;
  line-height: 1.7;
  margin-bottom: var(--spacing-lg);
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
}

.privacy-request-btn {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-sm);
  background: white;
  color: var(--primary-green);
  padding: var(--spacing-md) var(--spacing-xl);
  border-radius: var(--border-radius-md);
  text-decoration: none;
  font-weight: 700;
  font-size: 1.2rem;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: var(--shadow-md);
  position: relative;
  z-index: 1;
  border: 2px solid transparent;
}

.privacy-request-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--shadow-xl);
  background: var(--neutral-light);
  border-color: white;
}

.privacy-request-btn i {
  transition: transform 0.3s ease;
}

.privacy-request-btn:hover i {
  transform: scale(1.2);
}

/* Enhanced responsive design */
@media (max-width: 1024px) {
  .privacy-content {
    padding: var(--spacing-lg);
  }

  .policy-content h1 {
    font-size: 2.5rem;
  }

  .policy-content h2 {
    font-size: 2rem;
  }

  .contact-methods {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 768px) {
  .privacy-content {
    margin: var(--spacing-md);
    padding: var(--spacing-md);
  }

  .policy-content h1 {
    font-size: 2.2rem;
  }

  .policy-content h2 {
    font-size: 1.8rem;
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .policy-content h3 {
    font-size: 1.4rem;
  }

  .contact-methods {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .contact-container h2 {
    font-size: 2rem;
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .privacy-request-cta {
    padding: var(--spacing-lg);
  }

  .privacy-request-cta h3 {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .policy-content h1 {
    font-size: 1.8rem;
  }

  .policy-content h2 {
    font-size: 1.5rem;
  }

  .policy-content h3 {
    font-size: 1.2rem;
  }

  .contact-card {
    padding: var(--spacing-lg);
  }

  .contact-icon {
    width: 60px;
    height: 60px;
    font-size: 1.8rem;
  }

  .privacy-request-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: 1.1rem;
  }
}

/* Loading states and animations */
.privacy-content.loading {
  opacity: 0.7;
  pointer-events: none;
}

.privacy-content.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  border: 4px solid var(--neutral-beige);
  border-top: 4px solid var(--primary-green);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Smooth scroll behavior for internal links */
html {
  scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.privacy-request-btn:focus,
.contact-card:focus {
  outline: 3px solid var(--accent-sage);
  outline-offset: 2px;
}

/* Selection styles */
::selection {
  background: var(--accent-sage);
  color: white;
}

::-moz-selection {
  background: var(--accent-sage);
  color: white;
}

/* Enhanced scroll reveal animations */
.privacy-content {
  opacity: 0;
  transform: translateY(30px);
  animation: fadeInUp 0.8s ease forwards;
}

.contact-card {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
}

.contact-card:nth-child(1) { animation-delay: 0.1s; }
.contact-card:nth-child(2) { animation-delay: 0.2s; }
.contact-card:nth-child(3) { animation-delay: 0.3s; }

.privacy-request-cta {
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.6s ease forwards;
  animation-delay: 0.4s;
}

/* Utility classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

/* Error and success states */
.error-message {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  border-left: 4px solid var(--error-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  color: #c62828;
  margin: var(--spacing-md) 0;
}

.success-message {
  background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
  border-left: 4px solid var(--success-color);
  padding: var(--spacing-md);
  border-radius: var(--border-radius-md);
  color: #2e7d32;
  margin: var(--spacing-md) 0;
}

/* Dark mode support (if needed in future) */
@media (prefers-color-scheme: dark) {
  .privacy-content {
    background: #1a1a1a;
    color: #e0e0e0;
  }

  .policy-content h1,
  .policy-content h2,
  .policy-content h3 {
    color: #4caf50;
  }

  .contact-card {
    background: #2a2a2a;
    color: #e0e0e0;
  }
}

/* Print styles */
@media print {
  .privacy-contact-section,
  .privacy-request-cta,
  .contact-methods {
    display: none;
  }

  .privacy-content {
    box-shadow: none;
    border: 1px solid #ccc;
    background: white;
  }

  .policy-content h1,
  .policy-content h2,
  .policy-content h3 {
    color: #000 !important;
  }

  .policy-content {
    font-size: 12pt;
    line-height: 1.4;
  }

  .policy-content h1 { font-size: 18pt; }
  .policy-content h2 { font-size: 16pt; }
  .policy-content h3 { font-size: 14pt; }

  /* Ensure content doesn't break across pages poorly */
  .policy-content h1,
  .policy-content h2,
  .policy-content h3 {
    page-break-after: avoid;
  }

  .policy-content p,
  .policy-content ul {
    page-break-inside: avoid;
  }
}
