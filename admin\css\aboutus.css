:root {
  /* Using color variables from common.css */
  --primary: var(--primary-green);
  --secondary: var(--accent-terracotta);
  --dark: var(--text-dark);
  --light: var(--neutral-light);
  --danger: var(--danger);
  --success: var(--success);
  --warning: var(--warning);
  --gray: var(--neutral-beige);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f8f9fa;
  color: #333;
  line-height: 1.6;
}

.dashboard-container {
  padding: 20px;
  margin-left: auto;
  margin-right: auto;
  max-width: 1400px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--gray);
}

.dashboard-title {
  color: var(--dark);
  font-size: 24px;
  font-weight: 600;
}

.dashboard-actions {
  display: flex;
  gap: 15px;
}

.btn {
  padding: 10px 15px;
  border-radius: 6px;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-primary {
  background-color: var(--primary-green);
  color: var(--text-light);
}

.btn-primary:hover {
  background-color: var(--accent-sage);
}

.btn-secondary {
  background-color: var(--accent-terracotta);
  color: var(--text-light);
}

.btn-secondary:hover {
  background-color: var(--accent-light-brown);
}

.btn-danger {
  background-color: var(--danger);
  color: var(--text-light);
}

.btn-danger:hover {
  background-color: #d45a3d;
}

.btn-success {
  background-color: var(--success);
  color: var(--text-light);
}

.btn-success:hover {
  background-color: #3d8b40;
}

.content-sections {
  display: flex;
  flex-wrap: wrap;
  gap: 25px;
  margin-top: 20px;
  padding: 0 15px;
}

.section-card {
  flex: 1;
  min-width: 380px;
  max-width: calc(50% - 25px); /* This ensures 2 cards per row with gap */
  background-color: var(--neutral-light);
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  padding: 20px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.section-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.section-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--gray);
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-dark);
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-actions {
  display: flex;
  gap: 10px;
}

.action-icon {
  font-size: 16px;
  color: var(--text-medium);
  cursor: pointer;
  transition: color 0.3s ease;
}

.action-icon:hover {
  color: var(--primary-green);
}

.section-preview {
  font-size: 14px;
  color: var(--text-medium);
  margin-bottom: 15px;
  max-height: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

.section-media {
  width: 100%;
  height: 150px;
  background-color: var(--neutral-beige);
  border-radius: 6px;
  margin-bottom: 15px;
  overflow: hidden;
  position: relative;
}

.section-media img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.section-stats {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  color: #6c757d;
}

.stat {
  display: flex;
  align-items: center;
  gap: 5px;
}

.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background-color: var(--neutral-light);
  border-radius: 10px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  padding: 30px;
  position: relative;
}

.modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  font-size: 20px;
  cursor: pointer;
  color: var(--text-medium);
  transition: color 0.3s ease;
}

.modal-close:hover {
  color: var(--danger);
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--neutral-beige);
  color: var(--text-dark);
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-control {
  width: 100%;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid var(--neutral-beige);
  transition: border-color 0.3s ease;
  font-size: 15px;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.25);
}

textarea.form-control {
  min-height: 150px;
  resize: vertical;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.image-uploader {
  border: 2px dashed var(--neutral-beige);
  border-radius: 6px;
  padding: 30px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.image-uploader:hover {
  border-color: var(--primary-green);
}

.alert {
  padding: 12px 20px;
  border-radius: 6px;
  margin-bottom: 20px;
  display: none;
}

.alert-success {
  background-color: #d1e7dd;
  color: #0f5132;
  border: 1px solid #badbcc;
}

.alert-danger {
  background-color: #f8d7da;
  color: #842029;
  border: 1px solid #f5c2c7;
}

@media (min-width: 1400px) {
  .sidebar-minimized .content-sections .section-card {
    max-width: calc(33.333% - 25px); /* This allows 3 cards per row */
  }
}

@media (max-width: 768px) {
  .content-sections {
    flex-direction: column;
    padding: 0 10px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .dashboard-actions {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .dashboard-actions .btn {
    flex: 1;
    min-width: 150px;
    justify-content: center;
  }

  .btn {
    padding: 8px 12px;
    font-size: 14px;
  }

  .section-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .section-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .modal {
    width: 95%;
    padding: 20px;
  }

  .section-card {
    min-width: 100%;
    max-width: 100%;
  }
}
