* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: var(--neutral-light);
  color: var(--text-dark);
  line-height: 1.6;
}

.hero-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url("../images/15.jpg");
  background-size: cover;
  background-position: center;
  z-index: -1;
  height: 100vh;
  filter: brightness(0.8);
}

.hero-content {
  position: relative;
  height: 70vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  color: var(--text-light);
  padding: 2rem;
  max-width: 800px;
  background-color: rgba(42, 72, 88, 0.7);
  border-radius: 12px;
  backdrop-filter: blur(5px);
}

h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

h2 {
  font-size: 2.5rem;
  margin-bottom: 2rem;
  font-weight: 500;
  color: var(--primary-green);
}

p {
  margin-bottom: 1.5rem;
  font-size: 1.1rem;
}

.earth-day-banner {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 1rem;
  text-align: center;
  font-size: 1.2rem;
}

.cta-button {
  display: inline-block;
  background-color: var(--accent-terracotta);
  color: var(--text-light);
  padding: 0.8rem 2rem;
  border-radius: 30px;
  font-weight: 600;
  text-decoration: none;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  margin-top: 1rem;
}

.cta-button:hover {
  background-color: var(--primary-brown);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.cta-button.added {
  background-color: var(--primary-green);
}

.cta-button.added:hover {
  background-color: var(--accent-terracotta);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 3rem 1rem;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background-color: var(--accent-terracotta);
  border-radius: 2px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 4rem;
}

.product-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.product-card:hover {
  transform: translateY(-10px);
}

.product-image {
  height: 250px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.05);
}

.product-info {
  padding: 1.5rem;
}

.product-title {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: var(--primary-green);
}

.product-price {
  font-size: 1.1rem;
  font-weight: 700;
  color: var(--accent-terracotta);
  margin-bottom: 1rem;
}

.sold-out-badge {
  background-color: var(--text-medium);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 4px;
  font-size: 0.8rem;
  display: inline-block;
  margin-bottom: 1rem;
}

.impact-section {
  background-color: var(--neutral-cream);
  padding: 4rem 0;
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 2rem;
}

.impact-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  padding: 1.5rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  flex-direction: column;
}

.impact-image {
  height: 200px;
  margin-bottom: 1.5rem;
}

.impact-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.impact-title {
  font-size: 1.3rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: var(--primary-green);
  text-align: center;
}

.impact-text {
  flex-grow: 1;
  margin-bottom: 1.5rem;
}

.link-button {
  align-self: flex-end;
  display: flex;
  align-items: center;
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

.link-button:hover {
  color: var(--accent-terracotta);
}

.link-button span {
  margin-right: 0.5rem;
}

.quote {
  text-align: center;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 1rem;
  font-style: italic;
  font-size: 1.3rem;
  line-height: 1.8;
}

.quote-author {
  font-weight: 600;
  margin-top: 1rem;
  font-style: normal;
}

.notification {
  position: fixed;
  top: 100px;
  right: 40px;
  background-color: var(--primary-green);
  color: white;
  padding: 1rem 2rem;
  border-radius: 6px;
  z-index: 1000;
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  .hero-content {
    padding: 1.5rem;
  }

  .products-grid,
  .impact-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }

  .quote {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }

  h2 {
    font-size: 1.5rem;
  }

  .hero-content {
    padding: 1rem;
  }

  .products-grid,
  .impact-grid {
    grid-template-columns: 1fr;
  }

  .quote {
    font-size: 1rem;
  }
}

/* Shopping Cart Styles */
.cart-button {
  position: fixed;
  right: 30px;
  bottom: 30px;
  background: #2c5545;
  color: white;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 1000;
}

.cart-button i {
  font-size: 24px;
}

.cart-count {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #e74c3c;
  color: white;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.cart-modal {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: white;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
  z-index: 1001;
  transition: right 0.3s ease-in-out;
}

.cart-modal.active {
  right: 0;
}

.cart-header {
  padding: 20px;
  background: #2c5545;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.cart-header h3 {
  margin: 0;
  font-size: 20px;
}

.close-cart {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
}

.cart-items {
  padding: 20px;
  max-height: calc(100vh - 180px);
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #eee;
  gap: 15px;
}

.cart-item img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.item-details {
  flex-grow: 1;
}

.item-details h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
}

.item-details p {
  margin: 0;
  color: #666;
}

.remove-btn {
  background: none;
  border: none;
  color: #e74c3c;
  font-size: 20px;
  cursor: pointer;
  padding: 5px 10px;
}

.cart-footer {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px;
  background: white;
  border-top: 1px solid #eee;
}

.cart-total {
  margin-bottom: 15px;
  font-size: 18px;
  font-weight: bold;
}

.checkout-btn {
  width: 100%;
  padding: 12px;
  background: #2c5545;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease;
}

.checkout-btn:hover {
  background: #1e3c30;
}

/* Cart Toggle Button */
.cart-toggle {
  background: #2c5545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.cart-toggle.in-cart {
  background: #1e3c30;
}

.cart-toggle:hover {
  background: #234536;
}

