/* Management Pages Styles */

/* <PERSON> Header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 2rem;
    padding: 2rem;
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-beige);
}

.page-header-content h1 {
    font-size: 2rem;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.page-header-content h1 i {
    color: var(--primary-green);
}

.page-header-content p {
    color: var(--text-medium);
    font-size: 1rem;
    margin: 0;
}

.page-header-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Alerts */
.alert {
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-md);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: 500;
}

.alert-success {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.alert-error {
    background-color: rgba(244, 67, 54, 0.1);
    color: #c62828;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.alert-warning {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.alert-info {
    background-color: rgba(33, 150, 243, 0.1);
    color: #1565c0;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

/* Filters Card */
.filters-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    margin-bottom: 2rem;
    border: 1px solid var(--neutral-beige);
    overflow: hidden;
}

.filters-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--neutral-beige);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--neutral-light) 0%, white 100%);
}

.filters-header h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filters-toggle {
    background: none;
    border: none;
    color: var(--text-medium);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: all 0.3s ease;
}

.filters-toggle:hover {
    background-color: var(--neutral-beige);
    color: var(--text-dark);
}

.filters-content {
    padding: 2rem;
}

.filters-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
}

.filter-group input,
.filter-group select {
    padding: 0.75rem;
    border: 1px solid var(--neutral-beige);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.filter-group input:focus,
.filter-group select:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.1);
}

.filters-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

/* Table Card */
.table-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-beige);
    overflow: hidden;
}

.table-header {
    padding: 1.5rem 2rem;
    border-bottom: 1px solid var(--neutral-beige);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--neutral-light) 0%, white 100%);
}

.table-title h3 {
    font-size: 1.3rem;
    color: var(--text-dark);
    margin: 0;
    font-weight: 600;
}

.table-actions {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.bulk-actions-form {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.bulk-actions-form select {
    padding: 0.5rem;
    border: 1px solid var(--neutral-beige);
    border-radius: var(--border-radius-sm);
    font-size: 0.85rem;
}

.table-container {
    overflow-x: auto;
}

/* Data Table */
.data-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.data-table th {
    background-color: var(--neutral-light);
    padding: 1rem;
    text-align: left;
    font-weight: 600;
    color: var(--text-dark);
    border-bottom: 1px solid var(--neutral-beige);
    white-space: nowrap;
}

.data-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--neutral-beige);
    vertical-align: top;
}

.data-table tr:hover {
    background-color: rgba(42, 72, 88, 0.02);
}

/* Program Info */
.program-info {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
    max-width: 400px;
}

.program-image {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    flex-shrink: 0;
}

.program-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.program-details h4 {
    font-size: 1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.program-details p {
    font-size: 0.85rem;
    color: var(--text-medium);
    line-height: 1.4;
    margin-bottom: 0.5rem;
}

.featured-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    background: linear-gradient(135deg, #ffc107 0%, #ff8f00 100%);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
}

/* Status Badges */
.status-badge {
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    background-color: rgba(76, 175, 80, 0.1);
    color: #2e7d32;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.status-completed {
    background-color: rgba(96, 125, 139, 0.1);
    color: #455a64;
    border: 1px solid rgba(96, 125, 139, 0.3);
}

.status-planned {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.status-cancelled {
    background-color: rgba(244, 67, 54, 0.1);
    color: #c62828;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.status-new {
    background-color: rgba(33, 150, 243, 0.1);
    color: #1565c0;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

.status-read {
    background-color: rgba(158, 158, 158, 0.1);
    color: #616161;
    border: 1px solid rgba(158, 158, 158, 0.3);
}

/* Country Badges */
.country-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    padding: 0.4rem 0.8rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 600;
}

.country-rwanda {
    background-color: rgba(0, 114, 188, 0.1);
    color: #0072bc;
    border: 1px solid rgba(0, 114, 188, 0.3);
}

.country-uganda {
    background-color: rgba(252, 209, 22, 0.1);
    color: #b8860b;
    border: 1px solid rgba(252, 209, 22, 0.3);
}

.country-congo {
    background-color: rgba(0, 123, 191, 0.1);
    color: #007bbf;
    border: 1px solid rgba(0, 123, 191, 0.3);
}

/* Category Tags */
.category-tag {
    background-color: var(--neutral-beige);
    color: var(--text-dark);
    padding: 0.3rem 0.6rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    font-weight: 500;
}

/* Beneficiaries Count */
.beneficiaries-count {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    color: var(--accent-terracotta);
    font-weight: 600;
}

/* Date Info */
.date-info {
    color: var(--text-medium);
    font-size: 0.85rem;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.btn-action {
    width: 32px;
    height: 32px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.btn-view {
    background-color: rgba(33, 150, 243, 0.1);
    color: #1565c0;
    border-color: rgba(33, 150, 243, 0.3);
}

.btn-view:hover {
    background-color: #1565c0;
    color: white;
    transform: translateY(-2px);
}

.btn-edit {
    background-color: rgba(255, 152, 0, 0.1);
    color: #ef6c00;
    border-color: rgba(255, 152, 0, 0.3);
}

.btn-edit:hover {
    background-color: #ef6c00;
    color: white;
    transform: translateY(-2px);
}

.btn-delete {
    background-color: rgba(244, 67, 54, 0.1);
    color: #c62828;
    border-color: rgba(244, 67, 54, 0.3);
}

.btn-delete:hover {
    background-color: #c62828;
    color: white;
    transform: translateY(-2px);
}

/* Empty State */
.empty-state {
    padding: 4rem 2rem;
    text-align: center;
    color: var(--text-medium);
}

.empty-state-icon {
    font-size: 4rem;
    color: var(--neutral-beige);
    margin-bottom: 1.5rem;
}

.empty-state h3 {
    font-size: 1.5rem;
    color: var(--text-dark);
    margin-bottom: 1rem;
    font-weight: 600;
}

.empty-state p {
    font-size: 1rem;
    line-height: 1.6;
    margin-bottom: 2rem;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

/* Pagination */
.pagination-wrapper {
    padding: 1.5rem 2rem;
    border-top: 1px solid var(--neutral-beige);
    background-color: var(--neutral-light);
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
}

.pagination-btn,
.pagination-number {
    padding: 0.5rem 1rem;
    border: 1px solid var(--neutral-beige);
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: all 0.3s ease;
    background: white;
}

.pagination-btn:hover,
.pagination-number:hover {
    background-color: var(--primary-green);
    color: white;
    border-color: var(--primary-green);
}

.pagination-number.active {
    background-color: var(--primary-green);
    color: white;
    border-color: var(--primary-green);
}

.pagination-numbers {
    display: flex;
    gap: 0.25rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .page-header {
        flex-direction: column;
        gap: 1.5rem;
        align-items: flex-start;
    }
    
    .page-header-actions {
        width: 100%;
        justify-content: flex-start;
    }
    
    .filters-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .table-header {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
    
    .table-actions {
        width: 100%;
        justify-content: flex-start;
    }
}

@media (max-width: 768px) {
    .page-header {
        padding: 1.5rem;
    }
    
    .page-header-content h1 {
        font-size: 1.5rem;
    }
    
    .filters-content {
        padding: 1.5rem;
    }
    
    .filters-grid {
        grid-template-columns: 1fr;
    }
    
    .filters-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .table-header {
        padding: 1rem 1.5rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.75rem;
    }
    
    .program-info {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .program-image {
        width: 100%;
        height: 120px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 0.25rem;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 0.25rem;
    }
    
    .pagination-btn,
    .pagination-number {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
}

@media (max-width: 480px) {
    .page-header {
        padding: 1rem;
    }
    
    .page-header-content h1 {
        font-size: 1.3rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .page-header-actions {
        flex-direction: column;
        gap: 0.75rem;
    }
    
    .filters-content {
        padding: 1rem;
    }
    
    .table-header {
        padding: 1rem;
    }
    
    .table-title h3 {
        font-size: 1.1rem;
    }
    
    .bulk-actions-form {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    .data-table {
        font-size: 0.8rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
    
    .empty-state {
        padding: 2rem 1rem;
    }
    
    .empty-state-icon {
        font-size: 3rem;
    }
    
    .empty-state h3 {
        font-size: 1.2rem;
    }
    
    .pagination-wrapper {
        padding: 1rem;
    }
}
