* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: var(--neutral-light);
  color: var(--text-dark);
  padding: 20px;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.dashboard-title {
  color: var(--primary-green);
  font-size: 24px;
}

.controls {
  display: flex;
  gap: 15px;
  align-items: center;
}

.month-selector {
  position: relative;
}

.month-dropdown {
  padding: 10px 15px;
  border: 2px solid var(--primary-green);
  border-radius: 8px;
  background-color: white;
  color: var(--text-dark);
  font-size: 16px;
  cursor: pointer;
  min-width: 180px;
}

.add-destination-btn {
  background-color: var(--primary-green);
  color: var(--text-light);
  border: none;
  border-radius: 8px;
  padding: 10px 20px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-destination-btn:hover {
  background-color: var(--accent-sage);
}

.destinations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.destination-card {
  background-color: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s, box-shadow 0.3s;
}

.destination-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.destination-image {
  height: 180px;
  width: 100%;
  background-color: var(--neutral-beige);
  position: relative;
  overflow: hidden;
}

.destination-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.destination-actions {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  gap: 5px;
}

.action-btn {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  background-color: rgba(255, 255, 255, 0.8);
  color: var(--text-dark);
  transition: background-color 0.3s, color 0.3s;
}

.edit-btn:hover {
  background-color: var(--accent-light-brown);
  color: white;
}

.delete-btn:hover {
  background-color: #e53935;
  color: white;
}

.destination-content {
  padding: 20px;
}

.destination-title {
  font-size: 20px;
  margin-bottom: 10px;
  color: var(--primary-green);
}

.destination-text {
  color: var(--text-medium);
  font-size: 14px;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.empty-state {
  text-align: center;
  padding: 80px 20px;
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.empty-state-icon {
  font-size: 60px;
  color: var(--neutral-beige);
  margin-bottom: 20px;
}

.empty-state-text {
  color: var(--text-medium);
  font-size: 18px;
  margin-bottom: 25px;
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
  display: none;
  overflow-y: scroll;
}

.modal {
  background-color: white;
  border-radius: 12px;
  width: 90%;
  max-width: 700px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
  overflow: scroll;
}

.modal-header {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  overflow: scroll;
}

.modal-title {
  font-size: 22px;
}

.modal-close {
  background: none;
  border: none;
  color: white;
  font-size: 24px;
  cursor: pointer;
  
}

.modal-body {
  padding: 25px;
  overflow: scroll;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--text-dark);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--neutral-beige);
  border-radius: 6px;
  font-size: 16px;
  color: var(--text-dark);
}

.form-textarea {
  min-height: 150px;
  resize: vertical;
}

.form-buttons {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
  margin-top: 30px;
}

.btn-cancel {
  background-color: var(--neutral-cream);
  color: var(--text-dark);
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  cursor: pointer;
}

.btn-save {
  background-color: var(--primary-green);
  color: white;
  border: none;
  padding: 12px 30px;
  border-radius: 6px;
  cursor: pointer;
}

.btn-save:hover {
  background-color: var(--accent-sage);
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .controls {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
  }

  .month-dropdown,
  .add-destination-btn {
    width: 100%;
  }

  .destinations-grid {
    grid-template-columns: 1fr;
  }
}

.preview-image {
  max-width: 100%;
  height: 150px;
  object-fit: cover;
  border-radius: 6px;
  margin-top: 10px;
  display: none;
}

.image-placeholder {
  height: 150px;
  background-color: var(--neutral-beige);
  border-radius: 6px;
  margin-top: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--text-medium);
}

.status-message {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 6px;
  text-align: center;
  display: none;
}

.success {
  background-color: #d4edda;
  color: #155724;
}

.error {
  background-color: #f8d7da;
  color: #721c24;
}
