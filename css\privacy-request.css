/* Privacy Request Page Styles */

.privacy-request-main {
  padding: 4rem 0;
  background: linear-gradient(135deg, var(--neutral-light) 0%, var(--neutral-cream) 100%);
  min-height: 80vh;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

/* Request Introduction */
.request-intro {
  text-align: center;
  margin-bottom: 4rem;
  padding: 2rem;
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.request-intro h2 {
  color: var(--primary-green);
  font-size: 2.5rem;
  margin-bottom: 1rem;
}

.request-intro p {
  font-size: 1.1rem;
  color: var(--text-medium);
  line-height: 1.6;
  max-width: 800px;
  margin: 0 auto;
}

/* Request Types Grid */
.request-types {
  margin-bottom: 4rem;
}

.request-types h3 {
  text-align: center;
  color: var(--primary-green);
  font-size: 2rem;
  margin-bottom: 2rem;
}

.request-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 2rem;
}

.request-type-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border-top: 4px solid var(--primary-green);
}

.request-type-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.type-icon {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
}

.request-type-card h4 {
  color: var(--primary-green);
  font-size: 1.3rem;
  margin-bottom: 1rem;
}

.request-type-card p {
  color: var(--text-medium);
  line-height: 1.5;
}

/* Form Section */
.request-form-section {
  margin-bottom: 4rem;
}

.request-form-section h3 {
  text-align: center;
  color: var(--primary-green);
  font-size: 2rem;
  margin-bottom: 2rem;
}

.form-container {
  background: white;
  padding: 3rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  max-width: 800px;
  margin: 0 auto;
}

.privacy-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  font-weight: 600;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.form-group input,
.form-group select,
.form-group textarea {
  padding: 1rem;
  border: 2px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-family: inherit;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 120px;
}

/* Checkbox Styles */
.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  cursor: pointer;
  font-size: 0.95rem;
  line-height: 1.5;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 20px;
  height: 20px;
  border: 2px solid var(--neutral-beige);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  flex-shrink: 0;
  margin-top: 2px;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-green);
  border-color: var(--primary-green);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  color: white;
  font-weight: bold;
  font-size: 14px;
}

.checkbox-label a {
  color: var(--primary-green);
  text-decoration: underline;
}

.checkbox-label a:hover {
  color: var(--accent-sage);
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  margin-top: 1rem;
}

.btn {
  padding: 1rem 2rem;
  border: none;
  border-radius: var(--border-radius);
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  text-decoration: none;
  min-width: 150px;
  justify-content: center;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-secondary {
  background: var(--neutral-beige);
  color: var(--text-dark);
}

.btn-secondary:hover {
  background: var(--neutral-cream);
  transform: translateY(-2px);
}

/* Info Cards */
.request-info {
  margin-bottom: 4rem;
}

.info-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.info-card {
  background: white;
  padding: 2rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  display: flex;
  align-items: flex-start;
  gap: 1.5rem;
}

.info-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--accent-sage), var(--primary-green));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
  flex-shrink: 0;
}

.info-card h4 {
  color: var(--primary-green);
  margin-bottom: 0.5rem;
  font-size: 1.2rem;
}

.info-card p {
  color: var(--text-medium);
  line-height: 1.5;
  margin: 0;
}

/* Contact Section */
.contact-section {
  background: white;
  padding: 3rem;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  text-align: center;
}

.contact-section h3 {
  color: var(--primary-green);
  font-size: 2rem;
  margin-bottom: 1rem;
}

.contact-section p {
  color: var(--text-medium);
  font-size: 1.1rem;
  line-height: 1.6;
  margin-bottom: 2rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.contact-info {
  display: flex;
  justify-content: center;
  gap: 3rem;
  flex-wrap: wrap;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1.1rem;
  color: var(--text-dark);
}

.contact-item i {
  color: var(--primary-green);
  font-size: 1.2rem;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(5px);
}

.modal-content {
  background-color: white;
  margin: 10% auto;
  border-radius: var(--border-radius);
  width: 90%;
  max-width: 500px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.modal-header {
  padding: 2rem;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  color: white;
  text-align: center;
}

.modal-header.error {
  background: linear-gradient(135deg, var(--danger), #e74c3c);
}

.modal-header h3 {
  margin: 0;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.modal-body {
  padding: 2rem;
  text-align: center;
}

.modal-body p {
  color: var(--text-medium);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.modal-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

/* Loading State */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.btn.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 0.5rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .request-types-grid {
    grid-template-columns: 1fr;
  }
  
  .form-container {
    padding: 2rem 1.5rem;
  }
  
  .form-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .info-cards {
    grid-template-columns: 1fr;
  }
  
  .contact-info {
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .modal-content {
    margin: 20% auto;
    width: 95%;
  }
  
  .checkbox-label {
    font-size: 0.9rem;
  }
}
