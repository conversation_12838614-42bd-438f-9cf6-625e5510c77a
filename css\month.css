.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.destine.contain {
  padding: 0;
  margin: 0 auto;
}

a {
  color: var(--primary-green);
  text-decoration: none;
  font-weight: 600;
  transition: color 0.3s ease;
}

a:hover {
  color: var(--accent-terracotta);
}

/* Navigation Path */
.nav-path {
  padding: 15px 20px;
  background-color: var(--neutral-light);
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  font-family: Arial, sans-serif;
}

.nav-path a {
  color: var(--primary-green);
  margin: 0 5px;
}

.nav-path span {
  color: var(--text-medium);
}

/* Hero Section */
.hero {
  position: relative;
  height: 75vh;
  overflow: hidden;
}

.hero-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transform: scale(1.02);
  transition: transform 6s ease;
}

.hero:hover .hero-image {
  transform: scale(1.1);
}

.hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
}

.hero-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  color: #fff;
  width: 90%;
  max-width: 800px;
}

.hero-content h1 {
  font-size: 3.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  opacity: 0;
  transform: translateY(20px);
  animation: fadeInUp 0.8s ease forwards 0.5s;
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Intro Text */
.intro-text {
  padding: 3rem 2rem;
  max-width: 900px;
  margin: 0 auto;
  text-align: center;
  line-height: 1.8;
  font-size: 1.2rem;
  color: #2c3e50;
}

/* Destine */
.destin {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.destine {
  display: flex;
  margin-bottom: 4rem;
  background: linear-gradient(to right, #fff, #f8f9fa);
  border-radius: 12px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  opacity: 0;
  animation: cardEntrance 0.8s ease forwards;
}

.destine:nth-child(even) {
  flex-direction: row-reverse;
}

.destine:nth-child(2) {
  animation-delay: 0.2s;
}

.destine:nth-child(3) {
  animation-delay: 0.4s;
}

.destine:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
}

.destine-image {
  flex: 1;
  min-height: 300px;
  position: relative;
  overflow: hidden;
}

.destine-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.destine:hover .destine-image img {
  transform: scale(1.05);
}

.destine-content {
  flex: 1;
  padding: 3rem;
}

.destine-content h2 {
  color: var(--primary-green);
  margin-bottom: 1.5rem;
  font-size: 2rem;
  position: relative;
}

.destine-content h2::after {
  content: "";
  position: absolute;
  bottom: -10px;
  left: 0;
  width: 60px;
  height: 3px;
  background: var(--accent-terracotta);
  transition: width 0.3s ease;
}

.destine:hover .destine-content h2::after {
  width: 100px;
}

.destine-content p {
  line-height: 1.6;
  color: #555;
  text-align: justify;
}

/* Month Navigation */
.month-nav {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  padding: 4rem 0;
  overflow: hidden;
}

.month-slider-container {
  position: relative;
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.month-slider {
  display: flex;
  gap: 20px;
  transition: transform 0.5s ease;
  width: fit-content;
}

.month-card {
  flex: 0 0 350px;
  position: relative;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.5s ease;
  transform: perspective(1000px);
}

.month-card:hover {
  transform: perspective(1000px) rotateY(5deg) translateY(-5px);
}

.month-card img {
  width: 100%;
  height: 250px;
  object-fit: cover;
}

.month-overlay {
  background: linear-gradient(
    to top,
    rgba(0, 0, 0, 0.8) 0%,
    rgba(0, 0, 0, 0.4) 50%,
    transparent 100%
  );
  height: 100%;
  display: flex;
  align-items: flex-end;
  padding: 2rem;
}

.month-name {
  font-size: 1.5rem;
  transform: translateY(20px);
  opacity: 0;
  transition: all 0.3s ease;
}

.month-card:hover .month-name {
  transform: translateY(0);
  opacity: 1;
}

.month-details {
  background: rgba(255, 255, 255, 0.95);
  padding: 1.5rem;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.month-card:hover .month-details {
  transform: translateY(0);
}

/* Slider Arrows */
.slider-arrow {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: var(--primary-green);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
  transition: all 0.3s ease;
  font-size: 1.2rem;
  opacity: 0;
}

.month-slider-container:hover .slider-arrow {
  opacity: 1;
}

.slider-arrow:hover {
  background: var(--accent-terracotta);
  transform: translateY(-50%) scale(1.1);
}

.slider-arrow.prev {
  left: 10px;
}

.slider-arrow.next {
  right: 10px;
}

/* Pagination dots styling */
.pagination {
  margin-top: 20px;
}

.pagination-dots {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.pagination-dot {
  width: 10px;
  height: 10px;
  background: #ccc;
  border-radius: 50%;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  transform-origin: center;
}

.pagination-dot:hover {
  transform: scale(1.5);
}

.pagination-dot.active {
  background: var(--accent-terracotta);
  box-shadow: 0 0 0 3px rgba(var(--accent-terracotta-rgb), 0.2);
}

.month-preview {
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  visibility: hidden;
  opacity: 0;
  transition: opacity 0.3s;
  white-space: nowrap;
}

.pagination-dot:hover .month-preview {
  visibility: visible;
  opacity: 1;
}

/* Scroll Indicator */
.scroll-indicator {
  position: absolute;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  60% {
    transform: translateY(-15px);
  }
}

/* Responsive */
@media (max-width: 992px) {
  .month-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .hero-content h1 {
    font-size: 2.8rem;
  }

  .destine-content {
    padding: 2rem;
  }
}

@media (max-width: 768px) {
  .destine {
    flex-direction: column !important;
  }

  .destine-image {
    min-height: 200px;
  }

  .hero-content h1 {
    font-size: 2.2rem;
  }

  .intro-text {
    font-size: 1.1rem;
    padding: 2rem 1.5rem;
  }

  .destine-content h2 {
    font-size: 1.8rem;
  }
}

@media (max-width: 576px) {
  .month-card {
    flex: 0 0 100%;
  }
}

/* Loading Animation */
@keyframes cardEntrance {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
