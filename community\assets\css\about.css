/* About Page Styles */
/* Comprehensive styling for the Virunga Ecotours Community About Page */

/* Page Header Styles */
.page-header {
    position: relative;
    height: 50vh;
    min-height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 0;
}

.page-header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.page-header-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.65);
    transition: transform 6s ease-in-out;
}

.page-header:hover .page-header-background img {
    transform: scale(1.03);
}

.page-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(42, 72, 88, 0.8) 0%,
        rgba(42, 72, 88, 0.65) 30%,
        rgba(139, 115, 85, 0.55) 70%,
        rgba(42, 72, 88, 0.8) 100%
    );
    z-index: -1;
}

.page-header-content {
    text-align: center;
    color: white;
    z-index: 1;
    max-width: 800px;
    padding: 0 16px;
    animation: fadeInUp 0.8s ease;
}

.page-header-content h1 {
    font-size: 2.25rem;
    font-weight: 600;
    margin-bottom: 1rem;
    line-height: 1.2;
    text-shadow: 1px 1px 4px rgba(0, 0, 0, 0.25);
    letter-spacing: -0.2px;
}

.page-header-content p {
    font-size: 1rem;
    opacity: 0.9;
    line-height: 1.5;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    max-width: 580px;
    margin: 0 auto;
    font-weight: 400;
}

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.8px;
    font-weight: 500;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
    transition: all 0.25s ease;
    padding: 0.2rem 0.4rem;
    border-radius: var(--border-radius-xs);
}

.breadcrumb a:hover {
    color: white;
    background: rgba(255, 255, 255, 0.08);
}

.breadcrumb .separator {
    color: rgba(255, 255, 255, 0.5);
    font-size: 0.75rem;
}

.breadcrumb .current {
    color: white;
    font-weight: 500;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Mission & Vision Section */
.mission-vision-section {
    padding: 60px 0;
    background: linear-gradient(
        135deg,
        var(--neutral-light) 0%,
        white 50%,
        var(--neutral-cream) 100%
    );
    position: relative;
    overflow: hidden;
}

.mission-vision-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23f0f0f0" opacity="0.25"/><circle cx="75" cy="75" r="1" fill="%23f0f0f0" opacity="0.25"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>') repeat;
    opacity: 0.4;
    z-index: 0;
}

.mission-vision-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
    position: relative;
    z-index: 1;
}

.mission-card,
.vision-card,
.values-card {
    background: white;
    border-radius: var(--border-radius-sm);
    padding: 1.5rem 1.25rem;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.05);
    transition: all 0.25s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(42, 72, 88, 0.06);
}

.mission-card::before,
.vision-card::before,
.values-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    transition: height 0.25s ease;
}

.mission-card::before {
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--accent-sage) 90%);
}

.vision-card::before {
    background: linear-gradient(90deg, var(--accent-terracotta) 0%, var(--accent-light-brown) 90%);
}

.values-card::before {
    background: linear-gradient(90deg, var(--accent-light-brown) 0%, var(--primary-brown) 90%);
}

.mission-card:hover,
.vision-card:hover,
.values-card:hover {
    transform: translateY(-6px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.mission-card:hover::before,
.vision-card:hover::before,
.values-card:hover::before {
    height: 6px;
}

.card-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.4rem;
    color: white;
    position: relative;
    overflow: hidden;
}

.mission-card .card-icon {
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 90%);
}

.vision-card .card-icon {
    background: linear-gradient(135deg, var(--accent-terracotta) 0%, var(--accent-light-brown) 90%);
}

.values-card .card-icon {
    background: linear-gradient(135deg, var(--accent-light-brown) 0%, var(--primary-brown) 90%);
}

.card-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.5s ease;
}

.mission-card:hover .card-icon::before,
.vision-card:hover .card-icon::before,
.values-card:hover .card-icon::before {
    left: 100%;
}

.mission-card h3,
.vision-card h3,
.values-card h3 {
    font-size: 1.5rem;
    color: var(--primary-green);
    margin-bottom: 1.25rem;
    font-weight: 600;
    text-align: center;
    line-height: 1.25;
}

.mission-card p,
.vision-card p {
    color: var(--text-medium);
    line-height: 1.5;
    font-size: 0.95rem;
    text-align: center;
}

.values-card ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.values-card li {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.6rem 0;
    color: var(--text-medium);
    font-weight: 500;
    font-size: 0.95rem;
    border-bottom: 1px solid rgba(42, 72, 88, 0.08);
    transition: all 0.25s ease;
}

.values-card li:last-child {
    border-bottom: none;
}

.values-card li:hover {
    color: var(--primary-green);
    transform: translateX(4px);
}

.values-card li i {
    color: var(--accent-terracotta);
    font-size: 0.9rem;
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
    transition: transform 0.3s ease;
}

.values-card li:hover i {
    transform: scale(1.2);
}

/* Story Section */
.story-section {
    padding: 70px 0;
    background: white;
    position: relative;
}

.story-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    align-items: center;
}

.story-text h2 {
    font-size: 1.85rem;
    color: var(--primary-green);
    margin-bottom: 1.5rem;
    font-weight: 600;
    position: relative;
    padding-bottom: 0.5rem;
}

.story-text h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-terracotta) 0%, var(--accent-light-brown) 90%);
    border-radius: 1.5px;
}

/* Timeline Styles */
.story-timeline {
    position: relative;
    padding-left: 1.5rem;
}

.story-timeline::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(
        to bottom,
        var(--primary-green) 0%,
        var(--accent-terracotta) 50%,
        var(--accent-light-brown) 100%
    );
    border-radius: 1px;
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
    padding-left: 2rem;
    animation: fadeInLeft 0.6s ease forwards;
    opacity: 0;
}

.timeline-item:nth-child(1) { animation-delay: 0.15s; }
.timeline-item:nth-child(2) { animation-delay: 0.3s; }
.timeline-item:nth-child(3) { animation-delay: 0.45s; }
.timeline-item:nth-child(4) { animation-delay: 0.6s; }

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.timeline-year {
    position: absolute;
    left: -3.25rem;
    top: 0;
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 90%);
    border-radius: var(--border-radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1rem;
    box-shadow: 0 4px 15px rgba(42, 72, 88, 0.2);
    border: 3px solid white;
}

.timeline-content h4 {
    font-size: 1.25rem;
    color: var(--primary-green);
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.timeline-content p {
    color: var(--text-medium);
    line-height: 1.5;
    font-size: 0.95rem;
}

/* Story Image and Stats */
.story-image {
    position: relative;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.story-image img {
    width: 100%;
    height: 360px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.story-image:hover img {
    transform: scale(1.03);
}

.story-stats {
    position: absolute;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(6px);
    border-radius: var(--border-radius-sm);
    padding: 1rem;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.75rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06);
}

.story-stats .stat {
    text-align: center;
}

.story-stats .stat-number {
    font-size: 1.6rem;
    font-weight: 600;
    color: var(--primary-green);
    margin-bottom: 0.25rem;
    line-height: 1;
}

.story-stats .stat-label {
    font-size: 0.85rem;
    color: var(--text-medium);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.4px;
}

/* Approach Section */
.approach-section {
    padding: 70px 0;
    background: linear-gradient(
        135deg,
        var(--neutral-light) 0%,
        var(--neutral-cream) 50%,
        var(--neutral-light) 100%
    );
    position: relative;
}

.approach-section .section-header {
    text-align: center;
    margin-bottom: 3.5rem;
}

.approach-section .section-header h2 {
    font-size: 2.25rem;
    color: var(--primary-green);
    margin-bottom: 1.25rem;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.approach-section .section-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-terracotta) 0%, var(--accent-light-brown) 90%);
    border-radius: 1.5px;
}

.approach-section .section-header p {
    font-size: 1.05rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
}

.approach-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.approach-item {
    background: white;
    border-radius: var(--border-radius-sm);
    padding: 2rem 1.5rem;
    text-align: center;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    border: 1px solid rgba(42, 72, 88, 0.04);
}

.approach-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 90%);
    opacity: 0;
    transition: opacity 0.25s ease;
    z-index: 0;
}

.approach-item:hover::before {
    opacity: 0.02;
}

.approach-item:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.approach-icon {
    width: 75px;
    height: 75px;
    border-radius: var(--border-radius-round);
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 90%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 1;
    transition: all 0.3s ease;
}

.approach-item:hover .approach-icon {
    transform: scale(1.05) rotate(3deg);
    box-shadow: 0 8px 20px rgba(42, 72, 88, 0.2);
}

.approach-item h3 {
    font-size: 1.35rem;
    color: var(--primary-green);
    margin-bottom: 1.25rem;
    font-weight: 600;
    position: relative;
    z-index: 1;
    transition: color 0.25s ease;
}

.approach-item p {
    color: var(--text-medium);
    line-height: 1.5;
    font-size: 0.95rem;
    position: relative;
    z-index: 1;
}

/* Team Section */
.team-section {
    padding: 70px 0;
    background: white;
    position: relative;
}

.team-section .section-header {
    text-align: center;
    margin-bottom: 3.5rem;
}

.team-section .section-header h2 {
    font-size: 2.25rem;
    color: var(--primary-green);
    margin-bottom: 1.25rem;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.team-section .section-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-terracotta) 0%, var(--accent-light-brown) 90%);
    border-radius: 1.5px;
}

.team-section .section-header p {
    font-size: 1.05rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
}

.team-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.team-member {
    background: white;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.06);
    transition: all 0.3s ease;
    border: 1px solid rgba(42, 72, 88, 0.04);
}

.team-member:hover {
    transform: translateY(-6px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.member-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.member-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.4s ease;
}

.team-member:hover .member-image img {
    transform: scale(1.05);
}

.member-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(42, 72, 88, 0.7) 0%,
        rgba(42, 72, 88, 0.5) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.25s ease;
}

.team-member:hover .member-overlay {
    opacity: 1;
}

.social-links {
    display: flex;
    gap: 0.75rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-round);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    text-decoration: none;
    transition: all 0.25s ease;
    backdrop-filter: blur(8px);
}

.social-links a:hover {
    background: white;
    color: var(--primary-green);
    transform: scale(1.05);
}

.member-info {
    padding: 1.75rem 1.5rem;
    text-align: center;
}

.member-info h3 {
    font-size: 1.25rem;
    color: var(--primary-green);
    margin-bottom: 0.35rem;
    font-weight: 600;
}

.member-title {
    color: var(--accent-terracotta);
    font-weight: 500;
    margin-bottom: 0.75rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.4px;
}

.member-bio {
    color: var(--text-medium);
    line-height: 1.5;
    font-size: 0.9rem;
    margin: 0;
}

.no-team-members {
    grid-column: 1 / -1;
    text-align: center;
    padding: 3rem 1.5rem;
    color: var(--text-medium);
}

.no-team-members i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--accent-terracotta);
    opacity: 0.6;
}

.no-team-members p {
    font-size: 1.1rem;
    margin: 0;
}

/* Impact Map Section */
.impact-map-section {
    padding: 70px 0;
    background: linear-gradient(
        135deg,
        var(--neutral-light) 0%,
        var(--neutral-cream) 100%
    );
    position: relative;
}

.impact-map-section .section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.impact-map-section .section-header h2 {
    font-size: 2.25rem;
    color: var(--primary-green);
    margin-bottom: 1.25rem;
    font-weight: 600;
    position: relative;
    display: inline-block;
}

.impact-map-section .section-header h2::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 70px;
    height: 3px;
    background: linear-gradient(90deg, var(--accent-terracotta) 0%, var(--accent-light-brown) 90%);
    border-radius: 1.5px;
}

.impact-map-section .section-header p {
    font-size: 1.05rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
}

/* Regional Statistics */
.regional-stats {
    margin-top: 4rem;
    padding: 3rem 0;
    background: linear-gradient(135deg, var(--neutral-light) 0%, var(--neutral-cream) 100%);
    border-radius: var(--border-radius-xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    padding: 0 2rem;
}

.stat-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    text-align: center;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    transition: height 0.3s ease;
}

.stat-card.rwanda::before {
    background: linear-gradient(90deg, #1565C0 0%, #0D47A1 100%);
}

.stat-card.uganda::before {
    background: linear-gradient(90deg, #2E7D32 0%, #1B5E20 100%);
}

.stat-card.drc::before {
    background: linear-gradient(90deg, #E65100 0%, #BF360C 100%);
}

.stat-card.total::before {
    background: linear-gradient(90deg, var(--primary-green) 0%, var(--accent-terracotta) 100%);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.stat-card:hover::before {
    height: 8px;
}

.stat-flag {
    font-size: 3rem;
    margin-bottom: 1rem;
    display: block;
}

.stat-card h4 {
    font-size: 1.3rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
    font-weight: 600;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-card.rwanda .stat-number {
    color: #1565C0;
}

.stat-card.uganda .stat-number {
    color: #2E7D32;
}

.stat-card.drc .stat-number {
    color: #E65100;
}

.stat-card.total .stat-number {
    color: var(--primary-green);
}

.stat-card .stat-label {
    font-size: 0.9rem;
    color: var(--text-medium);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.stat-card p {
    color: var(--text-medium);
    font-size: 0.9rem;
    margin: 0;
    font-style: italic;
}

/* CTA Section Enhancement */
.cta-section {
    padding: 120px 0;
    background: linear-gradient(
        135deg,
        var(--primary-green) 0%,
        var(--accent-sage) 50%,
        var(--primary-green) 100%
    );
    color: white;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.cta-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>') repeat;
    z-index: 0;
}

.cta-content {
    position: relative;
    z-index: 1;
}

.cta-content h2 {
    font-size: 3rem;
    margin-bottom: 1.5rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
}

.cta-content p {
    font-size: 1.3rem;
    margin-bottom: 3rem;
    opacity: 0.95;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
    line-height: 1.7;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

.cta-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-buttons .btn {
    padding: 18px 36px;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.cta-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.cta-buttons .btn:hover::before {
    left: 100%;
}

.cta-buttons .btn-outline {
    border-color: white;
    color: white;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.cta-buttons .btn-outline:hover {
    background: white;
    color: var(--primary-green);
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.3);
}

/* Responsive Design */
@media (max-width: 1200px) {
    .story-content {
        gap: 3rem;
    }

    .story-stats {
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
        padding: 1.5rem;
    }

    .story-stats .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 1024px) {
    .mission-vision-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
    }

    .story-content {
        grid-template-columns: 1fr;
        gap: 4rem;
    }

    .story-text {
        order: 2;
    }

    .story-image {
        order: 1;
    }

    .approach-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }

    .team-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 2rem;
    }
}

@media (max-width: 768px) {
    .page-header {
        height: 45vh;
        min-height: 320px;
    }

    .page-header-content h1 {
        font-size: 2rem;
    }

    .page-header-content p {
        font-size: 0.95rem;
    }

    .breadcrumb {
        flex-direction: column;
        gap: 0.5rem;
    }

    .breadcrumb .separator {
        display: none;
    }

    .mission-vision-section,
    .story-section,
    .approach-section,
    .team-section,
    .impact-map-section,
    .cta-section {
        padding: 80px 0;
    }

    .mission-vision-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .mission-card,
    .vision-card,
    .values-card {
        padding: 2rem 1.5rem;
    }

    .story-text h2,
    .approach-section .section-header h2,
    .team-section .section-header h2,
    .impact-map-section .section-header h2 {
        font-size: 2.5rem;
    }

    .story-timeline {
        padding-left: 1rem;
    }

    .timeline-year {
        left: -2.5rem;
        width: 60px;
        height: 60px;
        font-size: 0.9rem;
    }

    .timeline-item {
        padding-left: 2rem;
    }

    .story-stats {
        position: static;
        margin-top: 2rem;
        background: white;
        grid-template-columns: repeat(3, 1fr);
    }

    .approach-grid {
        grid-template-columns: 1fr;
    }

    .approach-item {
        padding: 2rem 1.5rem;
    }

    .team-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        padding: 0 1rem;
    }

    .stat-card {
        padding: 1.5rem 1rem;
    }

    .stat-flag {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .stat-card .stat-number {
        font-size: 2rem;
    }

    .cta-content h2 {
        font-size: 2.5rem;
    }

    .cta-content p {
        font-size: 1.1rem;
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 12px;
    }

    .page-header-content h1 {
        font-size: 1.75rem;
    }

    .page-header-content p {
        font-size: 0.9rem;
    }

    .mission-vision-section,
    .story-section,
    .approach-section,
    .team-section,
    .impact-map-section,
    .cta-section {
        padding: 40px 0;
    }

    .mission-card,
    .vision-card,
    .values-card {
        padding: 1.5rem 1rem;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .mission-card h3,
    .vision-card h3,
    .values-card h3 {
        font-size: 1.4rem;
    }

    .story-text h2,
    .approach-section .section-header h2,
    .team-section .section-header h2,
    .impact-map-section .section-header h2 {
        font-size: 2rem;
    }

    .timeline-year {
        left: -2rem;
        width: 50px;
        height: 50px;
        font-size: 0.8rem;
    }

    .timeline-item {
        padding-left: 1.5rem;
        margin-bottom: 2rem;
    }

    .timeline-content h4 {
        font-size: 1.2rem;
    }

    .story-stats {
        grid-template-columns: 1fr;
        gap: 1rem;
        text-align: center;
    }

    .story-stats .stat-number {
        font-size: 1.8rem;
    }

    .approach-icon {
        width: 80px;
        height: 80px;
        font-size: 2rem;
    }

    .approach-item h3 {
        font-size: 1.3rem;
    }

    .member-info {
        padding: 2rem 1.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0 0.5rem;
    }

    .stat-card {
        padding: 1rem;
    }

    .stat-flag {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
    }

    .stat-card h4 {
        font-size: 1.1rem;
    }

    .stat-card .stat-number {
        font-size: 1.8rem;
    }

    .stat-card .stat-label {
        font-size: 0.8rem;
    }

    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .cta-buttons .btn {
        padding: 14px 28px;
        font-size: 0.9rem;
        width: 100%;
        max-width: 280px;
    }
}

/* Print Styles */
@media print {
    .page-header,
    .impact-map-section,
    .cta-section {
        display: none;
    }

    .mission-vision-section,
    .story-section,
    .approach-section,
    .team-section {
        padding: 40px 0;
        break-inside: avoid;
    }

    .mission-vision-grid,
    .approach-grid,
    .team-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .mission-card,
    .vision-card,
    .values-card,
    .approach-item,
    .team-member {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    .story-content {
        grid-template-columns: 1fr;
    }

    .story-image {
        display: none;
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .mission-card,
    .vision-card,
    .values-card,
    .approach-item,
    .team-member {
        border: 2px solid var(--text-dark);
    }

    .timeline-year,
    .approach-icon,
    .card-icon {
        border: 2px solid var(--text-dark);
    }
}

/* Focus Visible for Better Accessibility */
.team-member:focus-visible,
.approach-item:focus-visible {
    outline: 3px solid var(--primary-green);
    outline-offset: 2px;
}

/* Loading Animation for Images */
.story-image img,
.member-image img {
    opacity: 0;
    animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
    to {
        opacity: 1;
    }
}

/* Scroll Animations */
.mission-card,
.vision-card,
.values-card,
.approach-item,
.team-member {
    opacity: 0;
    transform: translateY(30px);
    animation: scrollFadeIn 0.8s ease forwards;
}

.mission-card { animation-delay: 0.1s; }
.vision-card { animation-delay: 0.2s; }
.values-card { animation-delay: 0.3s; }

.approach-item:nth-child(1) { animation-delay: 0.1s; }
.approach-item:nth-child(2) { animation-delay: 0.2s; }
.approach-item:nth-child(3) { animation-delay: 0.3s; }
.approach-item:nth-child(4) { animation-delay: 0.4s; }

.team-member:nth-child(1) { animation-delay: 0.1s; }
.team-member:nth-child(2) { animation-delay: 0.2s; }
.team-member:nth-child(3) { animation-delay: 0.3s; }
.team-member:nth-child(4) { animation-delay: 0.4s; }

@keyframes scrollFadeIn {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
