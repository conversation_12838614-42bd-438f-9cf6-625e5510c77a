<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Logging Out - Virunga Ecotours Admin</title>
    <link
      rel="shortcut icon"
      href="../../images/logos/icon.png"
      type="image/x-icon"
    />
    <link rel="stylesheet" href="../css/common.css" />
    <link rel="stylesheet" href="../css/login.css" />
    <style>
      .logout-message {
        text-align: center;
        margin: 20px 0;
      }
      .spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        width: 36px;
        height: 36px;
        border-radius: 50%;
        border-left-color: #09f;
        animation: spin 1s linear infinite;
        margin: 20px auto;
      }
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
    </style>
  </head>
  <body>
    <div class="login-container">
      <div class="login-card">
        <div class="card-header">
          <img src="../images/icon.png" alt="Logo" class="logo" />
          <h1>Logging Out</h1>
        </div>
        <div class="logout-message">
          <p>Please wait while we log you out...</p>
          <div class="spinner"></div>
        </div>
        <div class="back-to-site">
          <a href="../../index.html" id="homeLink">
            <i class="fas fa-home"></i>
            Back to Website
          </a>
        </div>
      </div>
    </div>

    <script>
      document.addEventListener('DOMContentLoaded', function() {
        // Send logout request to the server
        fetch('../handlers/logoutHandler.php', {
          method: 'POST',
          credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            // Redirect to login page after successful logout
            setTimeout(() => {
              window.location.href = 'login.html';
            }, 1500);
          } else {
            // Show error message
            document.querySelector('.logout-message p').textContent = 'Error logging out. Please try again.';
            document.querySelector('.spinner').style.display = 'none';
          }
        })
        .catch(error => {
          console.error('Error:', error);
          document.querySelector('.logout-message p').textContent = 'Error logging out. Please try again.';
          document.querySelector('.spinner').style.display = 'none';
        });
      });
    </script>
  </body>
</html>