/* Program Detail Page Specific Styles */

/* Program Hero Section */
.program-hero {
    position: relative;
    height: 50vh;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-top: -90px;
    padding-top: 90px;
}

.program-hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.program-hero-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.program-hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(42, 72, 88, 0.5) 0%, rgba(42, 72, 88, 0.7) 100%);
    z-index: 100;
}

.program-hero-content {
    color: white;
    max-width: 1000px;
    width: 100%;
    padding: 0 20px;
}

.program-hero .breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    opacity: 0.9;
}

.program-hero .breadcrumb a {
    color: white;
    text-decoration: none;
    transition: color 0.3s ease;
}

.program-hero .breadcrumb a:hover {
    color: var(--neutral-cream);
}

.program-hero .breadcrumb .separator {
    opacity: 0.6;
}

.program-hero .breadcrumb .current {
    opacity: 0.8;
}

.program-hero-info h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.25);
    line-height: 1.2;
    text-align: center;
}

.program-hero-description {
    font-size: 1.1rem;
    opacity: 0.95;
    line-height: 1.5;
    margin-bottom: 1.5rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    text-align: center;
}

.program-badges {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
}

.program-country, .program-category, .program-featured {
    background: rgba(255, 255, 255, 0.95);
    color: var(--primary-green);
    padding: 0.6rem 1rem;
    border-radius: var(--border-radius-sm);
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.program-featured {
    background: rgba(255, 193, 7, 0.95);
    color: var(--text-dark);
}

.program-hero-stats {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 2rem;
    margin-top: 2rem;
}

.hero-stat {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    padding: .5rem;
    border-radius: var(--border-radius-sm);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
}

.hero-stat i {
    font-size: 1.5rem;
    color: var(--neutral-cream);
    opacity: 0.8;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number, .stat-text {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: #ddd;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-active {
    color: #4caf50;
    background-color: transparent;
}

.status-completed {
    color: #607d8b;
    background-color: transparent;
}

.status-planned {
    color: #ff9800;
    background-color: transparent;
}

/* Program Content Section */
.program-content-section {
    padding: 4rem 0;
    background-color: var(--neutral-light);
}

.program-content-grid {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 2rem;
    align-items: start;
}

.program-main-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.content-block {
    background: white;
    padding: 1.75rem;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.content-block h2 {
    font-size: 2rem;
    color: var(--primary-green);
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.program-description, .impact-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-medium);
}

/* Gallery Styles */
.program-gallery {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 0.75rem;
}

.gallery-item {
    position: relative;
    aspect-ratio: 4/3;
    border-radius: var(--border-radius-sm);
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.25s ease;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(42, 72, 88, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    font-size: 2rem;
    color: white;
}

/* Sidebar Styles */
.program-sidebar {
    display: flex;
    flex-direction: column;
    gap: 2rem;
    position: sticky;
    top: 2rem;
}

.sidebar-block {
    background: white;
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-sm);
}

.sidebar-block h3 {
    font-size: 1.1rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
    font-weight: 600;
}

.program-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.detail-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid var(--neutral-beige);
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-item i {
    font-size: 1.1rem;
    color: var(--accent-terracotta);
    width: 20px;
    text-align: center;
}

.detail-content {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.detail-label {
    font-size: 0.85rem;
    color: var(--text-medium);
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 0.25rem;
}

.detail-value {
    font-weight: 600;
    color: var(--text-dark);
}

/* Action Buttons */
.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.btn-block {
    width: 100%;
    text-align: center;
    justify-content: center;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1.1rem;
}

/* Share Buttons */
.share-buttons {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.75rem;
}

.share-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.share-btn.facebook {
    background-color: #1877f2;
    color: white;
}

.share-btn.twitter {
    background-color: #1da1f2;
    color: white;
}

.share-btn.linkedin {
    background-color: #0077b5;
    color: white;
}

.share-btn.email {
    background-color: var(--text-medium);
    color: white;
}

.share-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h2 {
    font-size: 2.5rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
    font-weight: 600;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Testimonials Section */
.program-testimonials-section {
    padding: 4rem 0;
    background: white;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.testimonial-card {
    background: var(--neutral-light);
    padding: 2rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    transition: transform 0.3s ease;
}

.testimonial-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
}

.testimonial-content {
    margin-bottom: 1.5rem;
}

.testimonial-rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.testimonial-rating .fa-star {
    color: var(--neutral-beige);
    font-size: 1rem;
}

.testimonial-rating .fa-star.active {
    color: #ffc107;
}

.testimonial-message {
    font-size: 1.1rem;
    line-height: 1.6;
    color: var(--text-medium);
    font-style: italic;
}

.testimonial-author {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.author-image, .author-image-placeholder {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-round);
    overflow: hidden;
    flex-shrink: 0;
}

.author-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.author-image-placeholder {
    background: var(--neutral-beige);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-medium);
    font-size: 1.5rem;
}

.author-info {
    flex: 1;
}

.author-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-green);
    margin-bottom: 0.25rem;
}

.author-role {
    font-size: 0.9rem;
    color: var(--text-medium);
    margin-bottom: 0.25rem;
}

.author-location {
    font-size: 0.85rem;
    color: var(--accent-terracotta);
    display: flex;
    align-items: center;
    gap: 0.3rem;
}

/* Related Programs Section */
.related-programs-section {
    padding: 4rem 0;
    background-color: var(--neutral-light);
}

.related-programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.related-program-card {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
    transition: all 0.3s ease;
}

.related-program-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.related-program-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.related-program-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.related-program-card:hover .related-program-image img {
    transform: scale(1.1);
}

.related-program-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, transparent 50%, rgba(0,0,0,0.7) 100%);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 1rem;
}

.related-program-content {
    padding: 1.5rem;
}

.related-program-content .program-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
    flex-wrap: wrap;
    gap: 0.5rem;
}

.related-program-content h3 {
    font-size: 1.3rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
    font-weight: 600;
    line-height: 1.3;
}

.related-program-content p {
    color: var(--text-medium);
    margin-bottom: 1rem;
    line-height: 1.6;
}

.related-program-content .program-stats {
    margin-bottom: 1.5rem;
}

/* Call to Action Section */
.program-cta-section {
    padding: 4rem 0;
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 100%);
    color: white;
}

.cta-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 3rem;
    flex-wrap: wrap;
}

.cta-text {
    flex: 1;
    min-width: 300px;
}

.cta-text h2 {
    font-size: 2.5rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.cta-text p {
    font-size: 1.2rem;
    opacity: 0.9;
    line-height: 1.6;
}

.cta-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Gallery Modal */
.gallery-modal {
    display: none;
    position: fixed;
    z-index: 9999;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.95);
    backdrop-filter: blur(5px);
}

.gallery-modal-content {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.gallery-modal-close {
    position: absolute;
    top: 2rem;
    right: 2rem;
    color: white;
    font-size: 3rem;
    font-weight: bold;
    cursor: pointer;
    z-index: 10001;
    transition: color 0.3s ease;
}

.gallery-modal-close:hover {
    color: var(--neutral-cream);
}

.gallery-modal-image-container {
    position: relative;
    max-width: 90%;
    max-height: 80%;
    display: flex;
    align-items: center;
    justify-content: center;
}

#galleryModalImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: var(--border-radius-md);
}

.gallery-nav-btn {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: none;
    width: 50px;
    height: 50px;
    border-radius: var(--border-radius-round);
    font-size: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.gallery-nav-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-50%) scale(1.1);
}

.gallery-prev {
    left: -70px;
}

.gallery-next {
    right: -70px;
}

.gallery-modal-info {
    text-align: center;
    color: white;
    margin-top: 2rem;
}

.gallery-modal-info h3 {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.gallery-modal-info p {
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .program-content-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .program-sidebar {
        position: static;
        order: -1;
    }

    .program-hero-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.75rem;
    }

    .cta-content {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
    }

    .program-hero-info h1 {
        font-size: 2.25rem;
    }
}

@media (max-width: 768px) {
    .program-hero {
        height: 45vh;
        min-height: 320px;
    }

    .program-hero-info h1 {
        font-size: 1.85rem;
    }

    .program-hero-description {
        font-size: 0.95rem;
    }

    .program-hero-stats {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .hero-stat {
        padding: 0.875rem;
    }

    .content-block {
        padding: 1.5rem;
    }

    .sidebar-block {
        padding: 1.5rem;
    }

    .section-header h2 {
        font-size: 2rem;
    }

    .testimonials-grid,
    .related-programs-grid {
        grid-template-columns: 1fr;
    }

    .program-gallery {
        grid-template-columns: repeat(2, 1fr);
    }

    .share-buttons {
        grid-template-columns: 1fr;
    }

    .cta-text h2 {
        font-size: 2rem;
    }

    .cta-actions {
        flex-direction: column;
        width: 100%;
    }

    .gallery-nav-btn {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .gallery-prev {
        left: -50px;
    }

    .gallery-next {
        right: -50px;
    }
}

@media (max-width: 480px) {
    .program-hero-info h1 {
        font-size: 1.5rem;
    }

    .program-hero-description {
        font-size: 0.875rem;
        margin-bottom: 1rem;
    }

    .program-badges {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .content-block {
        padding: 1rem;
    }

    .sidebar-block {
        padding: 1rem;
    }

    .content-block h2 {
        font-size: 1.5rem;
    }

    .program-gallery {
        grid-template-columns: 1fr;
    }

    .gallery-modal-content {
        padding: 1rem;
    }

    .gallery-modal-close {
        top: 1rem;
        right: 1rem;
        font-size: 2rem;
    }

    .gallery-nav-btn {
        display: none;
    }

    .section-header h2 {
        font-size: 1.8rem;
    }

    .cta-text h2 {
        font-size: 1.8rem;
    }
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background-color: var(--primary-green);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-sage);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-outline:hover {
    background-color: var(--primary-green);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Utility Classes */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Animation Classes */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid var(--neutral-beige);
    border-top: 2px solid var(--primary-green);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Back to Top Button */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background-color: var(--primary-green);
    color: white;
    border: none;
    border-radius: var(--border-radius-round);
    font-size: 1.2rem;
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
    transition: all 0.3s ease;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    background-color: var(--accent-sage);
    transform: translateY(-5px);
    box-shadow: var(--shadow-xl);
}

/* Notification System */
.notification {
    position: fixed;
    top: 2rem;
    right: 2rem;
    background: white;
    color: var(--text-dark);
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius-md);
    box-shadow: var(--shadow-lg);
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10000;
    max-width: 300px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-success {
    border-left: 4px solid var(--success-color);
}

.notification-error {
    border-left: 4px solid var(--error-color);
}

.notification i {
    font-size: 1.2rem;
}

.notification-success i {
    color: var(--success-color);
}

.notification-error i {
    color: var(--error-color);
}

/* Print Styles */
@media print {
    .program-hero {
        height: auto;
        min-height: auto;
        page-break-inside: avoid;
    }

    .program-hero-background {
        display: none;
    }

    .program-sidebar,
    .gallery-modal,
    .back-to-top,
    .notification {
        display: none;
    }

    .program-content-grid {
        grid-template-columns: 1fr;
    }

    .content-block {
        box-shadow: none;
        border: 1px solid #ddd;
        page-break-inside: avoid;
    }

    .program-gallery {
        display: none;
    }

    .share-buttons {
        display: none;
    }

    .cta-actions {
        display: none;
    }
}
