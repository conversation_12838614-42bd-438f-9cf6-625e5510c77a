/* Admin Forms Styles */

/* Form Layout */
.admin-form {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--neutral-beige);
    overflow: hidden;
}

.form-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 0;
}

/* Form Sections */
.form-section {
    border-bottom: 1px solid var(--neutral-beige);
}

.form-section:last-child {
    border-bottom: none;
}

.form-section-header {
    padding: 1.5rem 2rem;
    background: linear-gradient(135deg, var(--neutral-light) 0%, white 100%);
    border-bottom: 1px solid var(--neutral-beige);
}

.form-section-header h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
    margin: 0;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.form-section-header h3 i {
    color: var(--primary-green);
}

.form-section-content {
    padding: 2rem;
}

/* Form Groups */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group:last-child {
    margin-bottom: 0;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
}

.form-group label.required::after {
    content: ' *';
    color: #e53e3e;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 0.875rem;
    border: 1px solid var(--neutral-beige);
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background-color: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.1);
}

.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
    border-color: #e53e3e;
}

.form-group input:invalid:focus,
.form-group select:invalid:focus,
.form-group textarea:invalid:focus {
    box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.form-group small {
    display: block;
    margin-top: 0.5rem;
    color: var(--text-medium);
    font-size: 0.8rem;
    line-height: 1.4;
}

/* Form Rows */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.form-row:last-child {
    margin-bottom: 0;
}

/* Textarea */
textarea {
    resize: vertical;
    min-height: 100px;
}

/* File Inputs */
input[type="file"] {
    padding: 0.5rem;
    border: 2px dashed var(--neutral-beige);
    background-color: var(--neutral-light);
    cursor: pointer;
    transition: all 0.3s ease;
}

input[type="file"]:hover {
    border-color: var(--primary-green);
    background-color: rgba(42, 72, 88, 0.05);
}

input[type="file"]:focus {
    outline: none;
    border-color: var(--primary-green);
    box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.1);
}

/* Checkbox Groups */
.checkbox-group {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    padding: 1rem;
    background-color: var(--neutral-light);
    border-radius: var(--border-radius-sm);
    border: 1px solid var(--neutral-beige);
}

.checkbox-group input[type="checkbox"] {
    width: auto;
    margin: 0;
    transform: scale(1.2);
}

.checkbox-group label {
    margin: 0;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
}

.checkbox-group label i {
    color: var(--accent-terracotta);
}

.checkbox-group small {
    margin-top: 0.25rem;
    margin-left: 0;
}

/* Radio Groups */
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid var(--neutral-beige);
    border-radius: var(--border-radius-sm);
    cursor: pointer;
    transition: all 0.3s ease;
}

.radio-option:hover {
    background-color: var(--neutral-light);
    border-color: var(--primary-green);
}

.radio-option input[type="radio"] {
    width: auto;
    margin: 0;
}

.radio-option label {
    margin: 0;
    cursor: pointer;
    font-weight: 500;
    flex: 1;
}

/* Select Styling */
select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 0.75rem center;
    background-size: 1rem;
    padding-right: 2.5rem;
}

/* Form Actions */
.form-actions {
    padding: 2rem;
    background-color: var(--neutral-light);
    border-top: 1px solid var(--neutral-beige);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    flex-wrap: wrap;
}

.form-actions .btn {
    min-width: 140px;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--border-radius-sm);
    text-decoration: none;
    font-weight: 600;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    white-space: nowrap;
}

.btn-primary {
    background-color: var(--primary-green);
    color: white;
}

.btn-primary:hover {
    background-color: var(--accent-sage);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-green);
    border: 2px solid var(--primary-green);
}

.btn-outline:hover {
    background-color: var(--primary-green);
    color: white;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--text-medium);
    color: white;
}

.btn-secondary:hover {
    background-color: var(--text-dark);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-danger {
    background-color: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background-color: #c53030;
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

.btn-large {
    padding: 1rem 2rem;
    font-size: 1rem;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: none !important;
}

/* Form Validation */
.form-group.has-error input,
.form-group.has-error select,
.form-group.has-error textarea {
    border-color: #e53e3e;
}

.form-group.has-error .error-message {
    display: block;
    color: #e53e3e;
    font-size: 0.8rem;
    margin-top: 0.5rem;
}

.form-group.has-success input,
.form-group.has-success select,
.form-group.has-success textarea {
    border-color: #38a169;
}

.error-message {
    display: none;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Image Preview */
.image-preview {
    margin-top: 1rem;
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
}

.image-preview-item {
    position: relative;
    width: 120px;
    height: 120px;
    border-radius: var(--border-radius-md);
    overflow: hidden;
    border: 2px solid var(--neutral-beige);
}

.image-preview-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.image-preview-remove {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    width: 24px;
    height: 24px;
    background-color: rgba(229, 62, 62, 0.9);
    color: white;
    border: none;
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.image-preview-remove:hover {
    background-color: #c53030;
    transform: scale(1.1);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .form-actions .btn {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .form-section-header {
        padding: 1rem 1.5rem;
    }
    
    .form-section-content {
        padding: 1.5rem;
    }
    
    .form-actions {
        padding: 1.5rem;
    }
    
    .form-section-header h3 {
        font-size: 1.1rem;
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}

@media (max-width: 480px) {
    .form-section-header {
        padding: 1rem;
    }
    
    .form-section-content {
        padding: 1rem;
    }
    
    .form-actions {
        padding: 1rem;
    }
    
    .form-group {
        margin-bottom: 1rem;
    }
    
    .checkbox-group {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
    
    .image-preview-item {
        width: 100px;
        height: 100px;
    }
}
