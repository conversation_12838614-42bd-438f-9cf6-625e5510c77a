* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.container {
  padding: 2rem;
}

.section-title {
  font-size: 1.8rem;
  color: var(--primary-green);
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  border-bottom: 2px solid var(--primary-brown);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.blogs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  padding: 1rem 0;
}

.blog-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.blog-img {
  height: 200px;
  overflow: hidden;
}

.blog-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.blog-content {
  padding: 1.25rem;
}

.blog-category {
  display: inline-block;
  background-color: var(--accent-sage);
  color: var(--text-light);
  font-size: 0.8rem;
  padding: 0.35rem 1rem;
  border-radius: 20px;
  margin-bottom: 1rem;
}

.blog-title {
  font-size: 1.1rem;
  color: var(--primary-green);
  margin-bottom: 1rem;
  line-height: 1.4;
}

.blog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  margin-top: 1rem;
  border-top: 1px solid #eee;
}

.blog-comments {
  color: var(--text-dark);
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-actions {
  display: flex;
  gap: 0.75rem;
  position: relative;
}

.action-menu-toggle {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  color: var(--text-dark);
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.action-menu-toggle:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.action-dropdown {
  position: absolute;
  right: 0;
  bottom: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 10;
  opacity: 0;
  visibility: hidden;
  transform: translateY(10px);
  transition: all 0.2s ease;
}

.action-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.action-btn {
  width: 100%;
  text-align: left;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-btn:hover {
  background-color: var(--neutral-cream);
}
.action-btn.edit {
  color: var(--primary-green);
}

.action-btn.view {
  color: var(--primary-brown);
}

.action-btn.delete {
  color: #dc2626;
}

.action-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .blogs-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .container {
    width: 100%;
    margin-left: 0;
    padding: 1rem;
  }

  .blogs-grid {
    grid-template-columns: 1fr;
  }

  .blog-card {
    max-width: 500px;
    margin: 0 auto;
  }
}

.create-button {
  background-color: var(--primary-green);
  color: var(--text-light);
  border: none;
  padding: 12px 24px;
  font-size: 16px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s;
  display: inline-flex;
  align-items: center;
  gap: 10px;
}

.create-button:hover {
  background-color: var(--accent-sage);
}
