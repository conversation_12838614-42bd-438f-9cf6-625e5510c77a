* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
  font-family: "Arial", sans-serif;
}

body {
  background-color: #f5f5f5;
  padding: 20px;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  background-color: #fff;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
}

h2 {
  color: #2c3e50;
  margin-bottom: 30px;
  text-align: center;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15px 20px;
}

.form-col {
  flex: 1;
  padding: 0 15px;
  min-width: 250px;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: #2c3e50;
}

input,
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

textarea {
  min-height: 120px;
  resize: vertical;
}

.section-title {
  margin: 30px 0 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
  color: #2c3e50;
}

.btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  font-size: 14px;
  transition: all 0.3s;
}

.btn:hover {
  background-color: #2980b9;
}

.add-btn {
  background-color: #2ecc71;
  margin-bottom: 20px;
}

.add-btn:hover {
  background-color: #27ae60;
}

.add-btn span {
  margin-right: 5px;
  font-size: 18px;
}

.remove-btn {
  background-color: #e74c3c;
  margin-left: 10px;
}

.remove-btn:hover {
  background-color: #c0392b;
}

.day-container {
  background-color: #f9f9f9;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #eee;
}

.day-title {
  margin-bottom: 15px;
  color: #2c3e50;
}

.image-upload {
  margin-bottom: 20px;
}

.image-preview {
  width: 100%;
  height: 200px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 10px;
  cursor: pointer;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  color: #666;
}

.image-preview.has-image {
  border: none;
  color: transparent;
}

.image-preview i {
  font-size: 24px;
  margin-right: 8px;
}

.highlight-images {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 20px;
}

.highlight-image {
  height: 150px;
}

.list-container {
  margin-bottom: 15px;
}

.list-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.list-item input {
  flex: 1;
}

.submit-btn {
  background-color: #3498db;
  color: white;
  border: none;
  padding: 15px 30px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-top: 30px;
  width: 100%;
  transition: all 0.3s;
}

.submit-btn:hover {
  background-color: #2980b9;
}

@media (max-width: 768px) {
  .form-col {
    flex: 100%;
  }

  .highlight-images {
    grid-template-columns: repeat(2, 1fr);
  }
}
