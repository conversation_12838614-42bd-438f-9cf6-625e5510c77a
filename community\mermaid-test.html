<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mermaid Test</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/mermaid/10.6.1/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .mermaid {
            text-align: center;
            background: #fafafa;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #ddd;
        }
        .error {
            color: red;
            background: #ffebee;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #f44336;
        }
    </style>
</head>
<body>
    <h1>Mermaid Diagram Test</h1>
    
    <div class="test-container">
        <h2>Test 1: Simple Flowchart</h2>
        <div class="mermaid">
            flowchart TD
                A[Start] --> B{Decision}
                B -->|Yes| C[Action 1]
                B -->|No| D[Action 2]
                C --> E[End]
                D --> E
        </div>
    </div>

    <div class="test-container">
        <h2>Test 2: Complex Flowchart (Similar to About Page)</h2>
        <div class="mermaid">
            flowchart LR
                subgraph REGION["VIRUNGA MASSIF REGION"]
                    subgraph UGANDA["UGANDA 🇺🇬"]
                        UG_MAIN["🏞️ MGAHINGA GORILLA<br/>NATIONAL PARK"]
                        UG_PROGRAMS["🎯 COMMUNITY PROGRAMS"]
                    end

                    subgraph RWANDA["RWANDA 🇷🇼"]
                        RW_MAIN["🏞️ VOLCANOES<br/>NATIONAL PARK"]
                        RW_PROGRAMS["🎯 COMMUNITY PROGRAMS"]
                    end
                end

                UG_MAIN -.->|Border| RW_MAIN
                UG_PROGRAMS <--> RW_PROGRAMS

                classDef uganda fill:#FFD700,stroke:#FF6B35,stroke-width:3px,color:#000
                classDef rwanda fill:#00A1C9,stroke:#0077BE,stroke-width:3px,color:#fff
                classDef programs fill:#8E24AA,stroke:#4A148C,stroke-width:2px,color:#fff

                class UG_MAIN uganda
                class RW_MAIN rwanda
                class UG_PROGRAMS,RW_PROGRAMS programs
        </div>
    </div>

    <script>
        // Initialize Mermaid
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            securityLevel: 'loose',
            maxTextSize: 90000,
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            }
        });

        // Error handling
        window.addEventListener('load', function() {
            setTimeout(() => {
                const mermaidElements = document.querySelectorAll('.mermaid');
                mermaidElements.forEach((element, index) => {
                    if (!element.querySelector('svg') && !element.querySelector('.error')) {
                        element.innerHTML = '<div class="error">❌ Mermaid diagram failed to render</div>';
                    }
                });
            }, 3000);
        });
    </script>
</body>
</html>
