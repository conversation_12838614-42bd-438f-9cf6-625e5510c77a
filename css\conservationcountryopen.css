* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: var(--neutral-light);
  color: var(--text-dark);
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.head {
  position: relative;
  height: 100vh;
  max-height: 700px;
  color: var(--text-light);
  display: flex;
  align-items: center;
  background-image: linear-gradient(
    rgba(42, 72, 88, 0.4),
    rgba(42, 72, 88, 0.6)
  );
  background-size: cover;
  background-position: center;
  background-blend-mode: overlay;
}

.header-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  z-index: -1;
}

.header-content {
  padding: 2rem;
  max-width: 800px;
}

h1 {
  font-size: 5rem;
  font-weight: 300;
  margin-bottom: 0.5rem;
  line-height: 1.2;
}

.subtitle {
  font-size: 1.2rem;
  max-width: 600px;
  margin-top: 1rem;
  position: relative;
  padding-top: 1.5rem;
}

.subtitle::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 80px;
  height: 4px;
  background-color: var(--accent-light-brown);
}

.nav {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background-color: rgba(42, 72, 88, 0.9);
  padding: 1rem 0;
  z-index: 1000;
  transition: all 0.3s ease;
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo {
  color: var(--text-light);
  font-size: 1.5rem;
  font-weight: 600;
  text-decoration: none;
}

.nav-links {
  display: flex;
  list-style: none;
}

.nav-links li {
  margin-left: 2rem;
}

.nav-links a {
  color: var(--text-light);
  text-decoration: none;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: var(--neutral-beige);
}

.mobile-menu-btn {
  display: none;
  background: none;
  border: none;
  color: var(--text-light);
  font-size: 1.5rem;
  cursor: pointer;
}

.section {
  padding: 5rem 0;
}

.section-header {
  text-align: center;
  margin-bottom: 3rem;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 400;
  color: var(--primary-green);
  position: relative;
  display: inline-block;
  padding-bottom: 1rem;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background-color: var(--accent-light-brown);
}

.about-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.about-img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.about-content h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: var(--primary-green);
}

.about-content ul {
  list-style: none;
  margin-left: 0;
}

.about-content li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 0.8rem;
}

.about-content li::before {
  content: "•";
  color: var(--accent-light-brown);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.conservation-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
}

.conservation-content {
  order: 1;
}

.conservation-img {
  order: 2;
}

.conservation-img img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.conservation-content h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: var(--primary-green);
}

.conservation-content p {
  margin-bottom: 1.5rem;
}

.btn {
  display: inline-block;
  padding: 0.8rem 2rem;
  background-color: var(--primary-green);
  color: var(--text-light);
  text-decoration: none;
  border-radius: 4px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  font-size: 1rem;
}

.btn:hover {
  background-color: var(--accent-sage);
  transform: translateY(-2px);
}

.community-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: center;
  background-color: var(--neutral-cream);
  border-radius: 8px;
  padding: 3rem;
}

.community-img img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.goals-challenges {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  margin-top: 4rem;
}

.goals,
.challenges {
  background-color: var(--neutral-light);
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.goals h3,
.challenges h3 {
  font-size: 1.8rem;
  margin-bottom: 1.5rem;
  color: var(--primary-green);
  text-align: center;
}

.goals ul,
.challenges ul {
  list-style: none;
}

.goals li,
.challenges li {
  position: relative;
  padding-left: 1.5rem;
  margin-bottom: 1rem;
}

.goals li::before,
.challenges li::before {
  content: "•";
  color: var(--accent-light-brown);
  font-weight: bold;
  position: absolute;
  left: 0;
}

.image-gallery {
  margin-top: 4rem;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  height: 250px;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

footer {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 3rem 0;
  margin-top: 5rem;
}

.footer-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 3rem;
}

.footer-col h4 {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  position: relative;
  padding-bottom: 1rem;
}

.footer-col h4::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 50px;
  height: 2px;
  background-color: var(--accent-light-brown);
}

.footer-col ul {
  list-style: none;
}

.footer-col ul li {
  margin-bottom: 0.8rem;
}

.footer-col ul li a {
  color: var(--neutral-beige);
  text-decoration: none;
  transition: color 0.3s ease;
}

.footer-col ul li a:hover {
  color: var(--text-light);
}

.copyright {
  text-align: center;
  padding-top: 2rem;
  margin-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

/* Media Queries */
@media (max-width: 992px) {
  h1 {
    font-size: 4rem;
  }

  .about-grid,
  .conservation-grid,
  .community-grid,
  .goals-challenges,
  .footer-grid {
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  .conservation-img {
    order: 1;
  }

  .conservation-content {
    order: 2;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  h1 {
    font-size: 3rem;
  }

  .subtitle {
    font-size: 1rem;
  }

  .mobile-menu-btn {
    display: block;
  }

  .nav-links {
    position: fixed;
    top: 60px;
    left: -100%;
    width: 100%;
    height: calc(100vh - 60px);
    background-color: var(--primary-green);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: left 0.3s ease;
  }

  .nav-links.active {
    left: 0;
  }

  .nav-links li {
    margin: 1.5rem 0;
  }

  .section {
    padding: 3rem 0;
  }

  .section-title {
    font-size: 2rem;
  }

  .about-content h2,
  .conservation-content h2 {
    font-size: 1.8rem;
  }

  .community-grid {
    padding: 2rem;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }
}
