<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Modern Rich Text Editor</title>
  <style>
    :root {
      --primary: #4f46e5;
      --background: #f9fafb;
      --text: #111827;
      --toolbar-bg: #ffffff;
      --toolbar-border: #e5e7eb;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: var(--background);
      margin: 0;
      padding: 2rem;
      color: var(--text);
    }

    .editor-container {
      background: white;
      border-radius: 10px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
      overflow: hidden;
      max-width: 800px;
      margin: 0 auto;
    }

    .toolbar {
      background: var(--toolbar-bg);
      border-bottom: 1px solid var(--toolbar-border);
      padding: 0.5rem;
      display: flex;
      gap: 0.5rem;
      flex-wrap: wrap;
    }

    .toolbar button {
      background: var(--primary);
      color: white;
      border: none;
      padding: 0.4rem 0.8rem;
      border-radius: 6px;
      cursor: pointer;
      transition: background 0.3s;
      font-size: 1rem;
    }

    .toolbar button:hover {
      background: #4338ca;
    }

    .editor {
      padding: 1rem;
      min-height: 300px;
      outline: none;
    }

    .editor:empty:before {
      content: attr(data-placeholder);
      color: #9ca3af;
      pointer-events: none;
    }
  </style>
</head>
<body>

<div class="editor-container">
  <div class="toolbar">
    <button onclick="format('bold')"><b>B</b></button>
    <button onclick="format('italic')"><i>I</i></button>
    <button onclick="format('underline')"><u>U</u></button>
    <button onclick="format('insertUnorderedList')">• List</button>
    <button onclick="format('insertOrderedList')">1. List</button>
    <button onclick="insertLink()">🔗 Link</button>
    <button onclick="format('unlink')">❌ Unlink</button>
    <button onclick="format('removeFormat')">🧹 Clear</button>
  </div>
  <div id="editor" class="editor" contenteditable="true" data-placeholder="Start writing here..."></div>
</div>

<script>
  function format(command, value = null) {
    document.execCommand(command, false, value);
  }

  function insertLink() {
    const url = prompt("Enter the URL:");
    if (url) {
      const title = prompt("Enter the link title:");
      if (title) {
        const selection = window.getSelection();
        if (selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          range.deleteContents();
          const a = document.createElement('a');
          a.href = url;
          a.textContent = title;
          a.target = '_blank'; // optional: open in new tab
          range.insertNode(a);
        }
      }
    }
  }
</script>

</body>
</html>
