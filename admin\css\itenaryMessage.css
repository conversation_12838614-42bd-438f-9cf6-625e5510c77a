/* Main container styles */
.bookings-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

/* Booking card styles */
.booking-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  transition: all 0.3s ease;
}

.booking-card:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Status indicator colors */
.booking-card.status-pending {
  border-left: 4px solid #ffc107;
}

.booking-card.status-confirmed {
  border-left: 4px solid #28a745;
}

.booking-card.status-cancelled {
  border-left: 4px solid #dc3545;
}

/* Header styles */
.booking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.booking-header h3 {
  margin: 0;
  font-size: 1.2rem;
  color: #333;
}

.booking-date {
  font-size: 0.8rem;
  color: #666;
}

.booking-status {
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: bold;
  text-transform: uppercase;
}

.status-pending .booking-status {
  background-color: #fff3cd;
  color: #856404;
}

.status-confirmed .booking-status {
  background-color: #d4edda;
  color: #155724;
}

.status-cancelled .booking-status {
  background-color: #f8d7da;
  color: #721c24;
}

/* Detail rows */
.detail-row {
  margin-bottom: 10px;
  display: flex;
  flex-wrap: wrap;
}

.detail-label {
  font-weight: bold;
  color: #555;
  width: 120px;
  flex-shrink: 0;
}

.detail-row p {
  margin: 5px 0 0 0;
  color: #666;
}

/* Form styles */
.status-form {
  margin-top: 20px;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #555;
}

.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.form-group textarea {
  min-height: 80px;
  resize: vertical;
}

.btn-primary {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #0069d9;
}

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .bookings-container {
    grid-template-columns: 1fr;
  }

  .detail-label {
    width: 100%;
    margin-bottom: 2px;
  }
}
