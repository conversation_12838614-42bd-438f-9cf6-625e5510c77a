* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--neutral-light);
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}
.hero {
  background: linear-gradient(rgba(42, 72, 88, 0.7), rgba(42, 72, 88, 0.7)),
    url("../images/15.jpg") center/cover no-repeat;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--neutral-light);
  text-align: center;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 3rem;
  margin-bottom: 20px;
  font-weight: 700;
}

.hero-content p {
  font-size: 1.2rem;
  max-width: 800px;
  margin: 0 auto;
}

.date {
  color: var(--neutral-beige);
  font-size: 0.9rem;
  margin-bottom: 10px;
}

main {
  padding: 50px 0;
}

article {
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  margin-bottom: 40px;
}

.article-content {
  padding: 30px;
}

h2 {
  color: var(--primary-green);
  margin-bottom: 20px;
  font-size: 2rem;
}

h3 {
  color: var(--accent-terracotta);
  margin: 30px 0 15px;
  font-size: 1.5rem;
}

p {
  margin-bottom: 20px;
  color: var(--text-medium);
}

.highlight-box {
  background-color: var(--neutral-cream);
  padding: 25px;
  border-radius: 8px;
  margin: 30px 0;
  border-left: 5px solid var(--accent-terracotta);
}

.highlight-box h3 {
  margin-top: 0;
}

.image-container {
  margin: 30px 0;
  border-radius: 8px;
  overflow: hidden;
}

.image-container img {
  width: 100%;
  display: block;
  transition: transform 0.3s;
}

.image-container:hover img {
  transform: scale(1.02);
}

.caption {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 10px 15px;
  font-size: 0.9rem;
  font-style: italic;
}

.related-posts {
  margin-top: 60px;
}

.related-posts h2 {
  margin-bottom: 30px;
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.post-card {
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s;
}

.post-card:hover {
  transform: translateY(-5px);
}

.post-card img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.post-content {
  padding: 20px;
}

.post-content h3 {
  margin-top: 0;
  font-size: 1.2rem;
}

.post-content .date {
  margin-bottom: 5px;
}

.cta-section {
  background-color: var(--accent-sage);
  padding: 50px 0;
  color: var(--text-light);
  text-align: center;
  margin-top: 50px;
}

.cta-container {
  max-width: 800px;
  margin: 0 auto;
}

.cta-section h2 {
  color: var(--neutral-light);
  margin-bottom: 20px;
}

.btn {
  display: inline-block;
  background-color: var(--accent-terracotta);
  color: var(--text-light);
  padding: 12px 30px;
  border-radius: 30px;
  text-decoration: none;
  font-weight: bold;
  margin-top: 20px;
  transition: all 0.3s;
}

.btn:hover {
  background-color: var(--primary-brown);
  transform: translateY(-3px);
}

@media (max-width: 768px) {
  .hero {
    height: 400px;
  }

  .hero-content h1 {
    font-size: 2.2rem;
  }

  .article-content {
    padding: 20px;
  }

  h2 {
    font-size: 1.8rem;
  }

  h3 {
    font-size: 1.3rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .hero {
    height: 300px;
  }

  .hero-content h1 {
    font-size: 1.8rem;
  }

  .posts-grid {
    grid-template-columns: 1fr;
  }
}
