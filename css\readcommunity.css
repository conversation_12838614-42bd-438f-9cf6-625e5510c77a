:root {
    --primary-green: #2a4858;
    --primary-brown: #8B7355;
    --accent-sage: #2a4858ac;
    --accent-terracotta: #967259;
    --accent-light-brown: #A68C69;
    --neutral-cream: #F2E8DC;
    --neutral-beige: #D8C3A5;
    --neutral-light: #F6F4F0;
    --neutral-dark: #3A3026;
    --text-dark: #3A3026;
    --text-medium: #5D4E41;
    --text-light: #F6F4F0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Arial', sans-serif;
}

body {
    background-color: var(--neutral-light);
    color: var(--text-dark);
}

.header-section {
    position: relative;
    height: 70vh;
    overflow: hidden;
}

.header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://gorillafund.org/wp-content/uploads/2022/02/Campus-tour-by-Student-and-teachers-from-<PERSON>.<PERSON>-<PERSON>ubi-9-Feb-2022-KRC-Cedric-26.jpg');
    background-size: cover;
    background-position: center;
    z-index: 1;
}

.header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 2;
}

.header-content {
    position: relative;
    z-index: 3;
    color: var(--text-light);
    padding: 100px 50px;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;
}

.header-content h1 {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.header-content p {
    font-size: 18px;
    line-height: 1.6;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
}

.main-content {
    padding: 60px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.section-title {
    font-size: 36px;
    color: var(--primary-green);
    margin-bottom: 30px;
}

.text-section {
    margin-bottom: 40px;
}

.text-section p {
    font-size: 16px;
    line-height: 1.8;
    margin-bottom: 20px;
    color: var(--text-medium);
}

.gallery-section {
    margin: 60px 0;
}

.main-image {
    width: 100%;
    height: 400px;
    object-fit: cover;
    margin-bottom: 20px;
    border-radius: 5px;
}

.thumbnail-container {
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.thumbnail {
    width: 24%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.thumbnail:hover {
    transform: scale(1.05);
}

.gorilla-footer {
    width: 100%;
    height: 500px;
    object-fit: cover;
}

.link {
    color: var(--primary-green);
    text-decoration: underline;
    font-weight: bold;
    cursor: pointer;
}

@media (max-width: 768px) {
    .header-content h1 {
        font-size: 36px;
    }
    
    .header-content p {
        font-size: 16px;
    }
    
    .section-title {
        font-size: 28px;
    }
    
    .thumbnail-container {
        flex-wrap: wrap;
    }
    
    .thumbnail {
        width: 48%;
        margin-bottom: 15px;
    }
}