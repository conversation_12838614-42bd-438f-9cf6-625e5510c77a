/* Home Content Sections */
.home-sections {
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.home-section {
  display: none;
  padding: 2rem;
  animation: fadeIn 0.3s ease-in-out;
}

.home-section.active {
  display: block;
}

.section-header {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--neutral-beige);
}

.section-header h2 {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.section-desc {
  color: var(--text-medium);
}

.section-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

/* Hero Preview */
.hero-preview {
  position: relative;
  height: 250px;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.hero-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Preview Container */
.content-preview {
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: var(--neutral-light);
  border-bottom: 1px solid var(--neutral-beige);
}

.preview-header h3 {
  font-size: 1.1rem;
  color: var(--text-dark);
}

.preview-toggle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  color: var(--text-medium);
  transition: all var(--transition-speed) ease;
}

.preview-toggle:hover {
  background-color: var(--neutral-light);
  color: var(--text-dark);
}

.preview-container {
  padding: 1.5rem;
  max-height: 500px;
  overflow-y: auto;
  background-color: #f9f9f9;
}

.hero-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 2rem;
  text-align: center;
  color: white;
}

.hero-overlay h2 {
  font-size: 1.8rem;
  margin-bottom: 1rem;
  color: white;
}

.hero-overlay p {
  font-size: 1rem;
  max-width: 80%;
}

/* Content Editor Styles */
.content-editor {
  background-color: white;
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  max-width: 450px;
  width: 100%;
}

/* File Upload Control */
.file-upload {
  position: relative;
  border: 2px dashed var(--neutral-beige);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-align: center;
  transition: border-color var(--transition-speed) ease;
}

.file-upload:hover {
  border-color: var(--primary-green);
}

.file-upload input[type="file"] {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  opacity: 0;
  cursor: pointer;
}

.file-preview {
  margin-bottom: 1rem;
  max-width: 100%;
  height: 150px;
  overflow: hidden;
  border-radius: var(--border-radius);
}

.file-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.file-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--primary-green);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: background-color var(--transition-speed) ease;
}

.file-button:hover {
  background-color: var(--accent-sage);
}

/* Hero Carousel Styles - Add to admin-styles.css */

.hero-carousel {
  position: relative;
  height: 250px;
  border-radius: var(--border-radius);
  overflow: hidden;
}

.carousel-navigation {
  position: absolute;
  bottom: 15px;
  left: 0;
  right: 0;
  z-index: 10;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 10px;
}

.carousel-arrow {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  width: 30px;
  height: 30px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.carousel-arrow:hover {
  background-color: rgba(255, 255, 255, 0.5);
}

.carousel-indicators {
  display: flex;
  gap: 8px;
}

.indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background-color: white;
  transform: scale(1.2);
}

.hero-slides {
  position: relative;
  height: 100%;
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease;
}

.hero-slide.active {
  opacity: 1;
  z-index: 1;
}

.hero-slide img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-carousel-settings {
  margin-top: 1.5rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--neutral-beige);
}

.hero-carousel-settings h3 {
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

/* Tabs Container */
.tabs-container {
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.tabs-header {
  display: flex;
  overflow-x: auto;
  background-color: var(--neutral-light);
  border-bottom: 1px solid var(--neutral-beige);
}

.tab-btn {
  padding: 1rem 1.5rem;
  background: none;
  border: none;
  border-right: 1px solid var(--neutral-beige);
  font-weight: 500;
  color: var(--text-medium);
  transition: all var(--transition-speed) ease;
}

.tab-btn.active {
  background-color: white;
  color: var(--primary-green);
  box-shadow: inset 0 3px 0 var(--primary-green);
}

.tab-btn:hover:not(.active) {
  background-color: rgba(0, 0, 0, 0.05);
}

.tab-content {
  display: none;
  padding: 1.5rem;
}

.tab-content.active {
  display: block;
  animation: fadeIn 0.3s ease-in-out;
}

.editor-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-weight: 500;
  color: var(--text-dark);
}

.form-group input[type="text"],
.form-group textarea,
.form-group select {
  padding: 0.75rem;
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
  font-size: 0.95rem;
  transition: border-color var(--transition-speed) ease;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus,
.form-group select:focus {
  border-color: var(--primary-green);
  outline: none;
}

.input-help {
  font-size: 0.85rem;
  color: var(--text-medium);
  margin-top: 0.25rem;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.hero-carousel {
  position: relative;
  height: 300px; /* increased height for better visibility */
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* added shadow for depth */
}

.carousel-arrow {
  background-color: rgba(255, 255, 255, 0.3);
  color: white;
  width: 40px; /* increased size for better usability */
  height: 40px;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: background-color 0.3s ease, transform 0.3s ease; /* added transform transition */
}

.carousel-arrow:hover {
  background-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.1); /* added hover effect */
}

.indicator {
  width: 12px; /* increased size for better visibility */
  height: 12px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.5);
  cursor: pointer;
  transition: all 0.3s ease;
}

.indicator.active {
  background-color: white;
  transform: scale(1.3); /* emphasized active state */
}

.hero-slide {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  transition: opacity 0.5s ease, transform 0.5s ease; /* added transform transition */
}

.hero-slide.active {
  opacity: 1;
  transform: scale(1.05); /* added slight zoom effect */
  z-index: 1;
}

@media (max-width: 1200px) {
  .section-content {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .about-preview {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .content-editor {
    max-width: 100%;
  }

  .tabs-header {
    overflow-x: auto;
  }

  .tab-btn {
    padding: 0.75rem 1rem;
    white-space: nowrap;
  }

  .home-section {
    padding: 1.5rem;
  }

  .cards-preview {
    flex-direction: column;
  }
}

.video-container {
  position: relative;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  height: 0;
  overflow: hidden;
  max-width: 100%;
  margin: 20px auto;
  border-radius: 8px;
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  transition: transform 0.3s ease;
}

.video-container:hover iframe {
  transform: scale(1.02);
}

@media (max-width: 768px) {
  .video-container {
    margin: 15px auto;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
  }
}

@media (max-width: 480px) {
  .video-container {
    margin: 10px auto;
    border-radius: 4px;
  }
}

/* Panel Actions Styles */
.panel-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.action-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  background-color: var(--primary-green);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-weight: 500;
  text-decoration: none;
  transition: all var(--transition-speed) ease;
  cursor: pointer;
}

.action-button:hover {
  background-color: var(--accent-sage);
  transform: translateY(-2px);
}

.action-button.secondary {
  background-color: var(--neutral-beige);
  color: var(--text-dark);
}

.action-button.secondary:hover {
  background-color: var(--accent-terracotta);
  color: white;
}

.action-button.delete-slide-btn {
  background-color: var(--danger);
  margin-top: 1rem;
}

.action-button.delete-slide-btn:hover {
  background-color: #d32f2f;
}

/* Modal Styles for Hero Slides */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-speed) ease;
}

.modal[style*="flex"] {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 100%;
  max-width: 500px;
  background-color: white;
  border-radius: var(--border-radius);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
  transform: translateY(-20px);
  transition: transform var(--transition-speed) ease;
  overflow: hidden;
  max-height: 90vh;
  overflow-y: auto;
}

.modal[style*="flex"] .modal-content {
  transform: translateY(0);
}

.modal-content h2 {
  padding: 1.5rem;
  margin: 0;
  background-color: var(--primary-green);
  color: white;
  border-bottom: 1px solid var(--neutral-beige);
}

.modal-content .close {
  position: absolute;
  top: 1rem;
  right: 1.5rem;
  font-size: 1.5rem;
  color: white;
  background: none;
  border: none;
  cursor: pointer;
  transition: color var(--transition-speed) ease;
}

.modal-content .close:hover {
  color: var(--neutral-beige);
}

.modal-content form {
  padding: 1.5rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--neutral-beige);
}

/* File preview in modal */
#newSlideImagePreview {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--neutral-light);
  border: 1px solid var(--neutral-beige);
  border-radius: var(--border-radius);
}

#newSlideImagePreview p {
  color: var(--text-medium);
  margin: 0;
}

#newSlideImagePreview img {
  max-width: 100%;
  height: auto;
  border-radius: var(--border-radius);
}
