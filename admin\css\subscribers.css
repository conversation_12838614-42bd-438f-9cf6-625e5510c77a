:root {
  --primary-green: #2a4858;
  --primary-brown: #8b7355;
  --accent-sage: #2a4858ac;
  --accent-terracotta: #967259;
  --accent-light-brown: #a68c69;
  --neutral-cream: #f2e8dc;
  --neutral-beige: #d8c3a5;
  --neutral-light: #f6f4f0;
  --neutral-dark: #3a3026;
  --text-dark: #3a3026;
  --text-medium: #5d4e41;
  --text-light: #f6f4f0;
}

.newcont {
  padding: 0 2rem 2rem 2rem;
}

.container {
  width: 100%;
  /* margin: 0 auto; */
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--neutral-beige);
}

header h1 {
  font-size: 1.8rem;
  color: var(--primary-green);
}

.download-btn {
  background-color: var(--primary-green);
  color: var(--text-light);
  border: none;
  padding: 12px 24px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: background-color 0.3s ease;
}

.download-btn:hover {
  background-color: var(--accent-sage);
}

table {
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

th,
td {
  padding: 16px;
  text-align: left;
  border-bottom: 1px solid var(--neutral-beige);
}

th {
  background-color: var(--neutral-cream);
  color: var(--text-medium);
  font-weight: 600;
}

tr:nth-child(even) {
  background-color: var(--neutral-light);
}

tr:hover {
  background-color: var(--neutral-cream);
}

.status-badge {
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 600;
  display: inline-block;
}

.status-badge.active {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.status-badge.inactive {
  background-color: #ffebee;
  color: #c62828;
}

.search-container {
  margin-bottom: 1.5rem;
}

.search-input {
  width: 100%;
  max-width: 400px;
  padding: 12px;
  border: 1px solid var(--neutral-beige);
  border-radius: 6px;
  font-size: 1rem;
}

.pagination {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  margin-top: 2rem;
}

.pagination-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--neutral-beige);
  background: white;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
  text-decoration: none;
  color: var(--text-dark);
}

.pagination-btn.active {
  background-color: var(--primary-green);
  color: white;
  border-color: var(--primary-green);
}

.pagination-btn:hover:not(.active) {
  background-color: #f5f5f5;
}

@media (max-width: 1024px) {
  .container {
    margin-left: 70px;
  }
}

/* Alert Messages */
.alert {
  padding: 12px 16px;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 10px;
  transition: opacity 0.3s ease;
}

.alert-success {
  background-color: #e8f5e9;
  color: #2e7d32;
  border: 1px solid #c8e6c9;
}

.alert-error {
  background-color: #ffebee;
  color: #c62828;
  border: 1px solid #ffcdd2;
}

/* Bulk Actions */
.bulk-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background-color: var(--neutral-light);
  border-radius: 6px;
  border: 1px solid var(--neutral-beige);
}

.bulk-select {
  display: flex;
  align-items: center;
  gap: 8px;
}

.bulk-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.bulk-action-select {
  padding: 8px 12px;
  border: 1px solid var(--neutral-beige);
  border-radius: 4px;
  background: white;
  font-size: 0.9rem;
}

.bulk-apply-btn {
  background-color: var(--primary-green);
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: background-color 0.3s ease;
}

.bulk-apply-btn:hover:not(:disabled) {
  background-color: var(--accent-sage);
}

.bulk-apply-btn:disabled {
  background-color: #ccc;
  cursor: not-allowed;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  background: none;
  border: none;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.9rem;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toggle-btn {
  color: var(--primary-green);
}

.toggle-btn:hover {
  background-color: var(--neutral-cream);
  color: var(--accent-sage);
}

.delete-btn {
  color: #c62828;
}

.delete-btn:hover {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  animation: fadeIn 0.3s ease;
}

.modal-content {
  background-color: white;
  margin: 15% auto;
  padding: 0;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.3s ease;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--neutral-beige);
}

.modal-header h3 {
  margin: 0;
  color: var(--primary-green);
}

.close-modal {
  color: #aaa;
  font-size: 28px;
  font-weight: bold;
  cursor: pointer;
  line-height: 1;
}

.close-modal:hover {
  color: #000;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 1.5rem;
  border-top: 1px solid var(--neutral-beige);
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #5a6268;
}

.btn-danger {
  background-color: #dc3545;
  color: white;
}

.btn-danger:hover {
  background-color: #c82333;
}

/* Checkbox Styles */
.select-all-checkbox,
.subscriber-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .container {
    margin-left: 0;
    padding: 1rem;
  }

  header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  table {
    display: block;
    overflow-x: auto;
    font-size: 0.875rem;
  }

  th,
  td {
    padding: 0.75rem 0.5rem;
  }

  .pagination {
    justify-content: center;
  }

  .bulk-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .bulk-controls {
    justify-content: center;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .modal-content {
    margin: 10% auto;
    width: 95%;
  }

  .modal-footer {
    flex-direction: column;
    gap: 10px;
  }

  .btn {
    width: 100%;
  }
}
