.container {
  padding: 20px;
}

.blog-form-container {
  /* display: none; */
  background-color: var(--neutral-cream);
  border-radius: 4px;
  padding: 30px;
  margin-top: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.form-header {
  margin-bottom: 30px;
  color: var(--primary-green);
  text-align: center;
}

.form-group {
  margin-bottom: 20px;
}

.form-row {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.form-col {
  flex: 1;
  min-width: 250px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 600;
  color: var(--text-medium);
}

input[type="text"],
input[type="number"],
input[type="file"],
select,
textarea {
  width: 100%;
  padding: 12px;
  border: 1px solid var(--neutral-beige);
  border-radius: 4px;
  background-color: var(--neutral-light);
  color: var(--text-dark);
  font-size: 14px;
}

textarea {
  resize: vertical;
  min-height: 100px;
}

.editor-toolbar {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  padding: 8px;
  background-color: var(--neutral-beige);
  border-radius: 4px;
  flex-wrap: wrap;
}

.editor-toolbar button {
  background-color: var(--neutral-light);
  color: var(--text-medium);
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.editor-toolbar button:hover {
  background-color: var(--accent-light-brown);
  color: var(--text-light);
}

.content-blocks {
  margin-top: 30px;
}

.content-block {
  background-color: var(--neutral-light);
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  position: relative;
}

.block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.block-title {
  font-weight: 600;
  color: var(--primary-green);
}

.remove-block {
  background-color: transparent;
  color: var(--accent-terracotta);
  border: none;
  cursor: pointer;
  font-size: 16px;
}

.add-block-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
  margin-bottom: 30px;
}

.add-block-btn {
  background-color: var(--accent-light-brown);
  color: var(--text-light);
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  font-size: 14px;
}

.add-block-btn:hover {
  background-color: var(--primary-brown);
}

.gallery-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
  margin-top: 20px;
}

.gallery-item {
  position: relative;
  background-color: var(--neutral-beige);
  padding: 15px;
  border-radius: 4px;
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.gallery-placeholder {
  width: 100%;
  height: 120px;
  background-color: var(--neutral-cream);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-medium);
  border-radius: 4px;
  margin-bottom: 10px;
}

.submit-row {
  margin-top: 40px;
  display: flex;
  justify-content: center;
  gap: 20px;
}

.submit-btn {
  background-color: var(--primary-green);
  color: var(--text-light);
  border: none;
  padding: 12px 40px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
}

.gallery-preview {
  position: relative;
  width: 100%;
  height: 150px;
  background-size: cover;
  background-position: center;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.gallery-preview:hover {
  transform: scale(1.02);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.gallery-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity 0.3s ease;
}

.gallery-preview:hover img {
  opacity: 0.9;
}

.gallery-item {
  margin-bottom: 20px;
  position: relative;
}

.gallery-item.empty .gallery-preview {
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.gallery-item.empty .gallery-preview i {
  font-size: 24px;
  color: #999;
}

@media screen and (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .gallery-container {
    grid-template-columns: repeat(2, 1fr);
  }

  .editor-toolbar {
    gap: 5px;
  }

  .submit-row {
    flex-direction: column;
  }
}

@media screen and (max-width: 480px) {
  .gallery-container {
    grid-template-columns: 1fr;
  }

  .content-block {
    padding: 15px;
  }

  .blog-form-container {
    padding: 20px 15px;
  }
}

#toast-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 9999;
}

.toast {
  min-width: 250px;
  margin-top: 10px;
  padding: 15px 20px;
  border-radius: 4px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  font-size: 14px;
  animation: toast-in 0.3s ease-in-out forwards;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.toast-success {
  background-color: #d4edda;
  border-left: 4px solid #28a745;
  color: #155724;
}

.toast-error {
  background-color: #f8d7da;
  border-left: 4px solid #dc3545;
  color: #721c24;
}

.toast-info {
  background-color: #d1ecf1;
  border-left: 4px solid #17a2b8;
  color: #0c5460;
}

.toast-close {
  background: none;
  border: none;
  color: inherit;
  font-size: 16px;
  cursor: pointer;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.toast-close:hover {
  opacity: 1;
}

@keyframes toast-in {
  from {
    transform: translateX(100%);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes toast-out {
  from {
    transform: translateX(0);
    opacity: 1;
  }

  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Editor Container */
.rich-editor-container {
  border: 1px solid #ddd;
  border-radius: 6px;
  overflow: hidden;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  background-color: #fff;
  margin-bottom: 15px;
  transition: all 0.2s ease;
}

.rich-editor-container:focus-within {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(39, 174, 96, 0.15);
}

/* Toolbar */
.rich-editor-toolbar {
  display: flex;
  flex-wrap: wrap;
  padding: 8px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
  gap: 2px;
}

.toolbar-btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  color: #555;
  transition: all 0.2s ease;
  position: relative;
}

.toolbar-btn:hover {
  background-color: #e9e9e9;
  color: var(--primary-green);
}

.toolbar-btn:active {
  background-color: #d9d9d9;
}

.toolbar-btn i {
  font-size: 14px;
}

.toolbar-divider {
  width: 1px;
  height: 24px;
  background-color: #e0e0e0;
  margin: 0 6px;
  align-self: center;
}

/* Editor Content Area */
.rich-editor-content {
  min-height: 150px;
  padding: 15px;
  outline: none;
  line-height: 1.6;
  color: #333;
  max-height: 400px;
  overflow-y: auto;
}

/* Placeholder */
.rich-editor-content.empty:not(.focused)::before {
  content: attr(data-placeholder);
  color: #aaa;
  position: absolute;
  pointer-events: none;
}

/* Common text formatting */
.rich-editor-content h1 {
  font-size: 24px;
  margin: 15px 0 10px;
}

.rich-editor-content h2 {
  font-size: 20px;
  margin: 12px 0 8px;
}

.rich-editor-content h3 {
  font-size: 18px;
  margin: 10px 0 6px;
}

.rich-editor-content h4 {
  font-size: 16px;
  margin: 8px 0 4px;
}

.rich-editor-content blockquote {
  border-left: 4px solid var(--primary-green);
  padding: 8px 15px;
  margin: 10px 0;
  background-color: #f9f9f9;
  color: #555;
  font-style: italic;
}

.rich-editor-content pre {
  background-color: #f5f5f5;
  padding: 10px;
  border-radius: 4px;
  font-family: monospace;
  white-space: pre-wrap;
  margin: 10px 0;
}

.rich-editor-content ul,
.rich-editor-content ol {
  padding-left: 25px;
  margin: 10px 0;
}

.rich-editor-content a {
  color: var(--primary-green);
  text-decoration: underline;
}

/* Focus state */
.rich-editor-content:focus {
  box-shadow: none;
}

/* Table styles for if we add table support */
.rich-editor-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
}

.rich-editor-content th,
.rich-editor-content td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.rich-editor-content th {
  background-color: #f5f5f5;
}
