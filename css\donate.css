

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: Arial, sans-serif;
}

body {
    background-color: var(--neutral-light);
    color: var(--text-dark);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.main-content {
    display: flex;
    flex-direction: row;
}

.left-content {
    flex: 3;
    padding: 0;
}

.hero-image {
    position: relative;
    height: 400px;
    overflow: hidden;
}

.hero-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}




.content-section {
    padding: 20px 30px;
}

.content-section h1 {
    font-size: 28px;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.content-section p {
    margin-bottom: 20px;
    color: var(--text-medium);
}

.content-section h3 {
    font-size: 18px;
    margin: 15px 0;
    color: var(--text-dark);
}

.right-content {
    flex: 2;
    padding: 20px;
    background-color: var(--neutral-light);
}

.tab-navigation {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.tab {
    padding: 10px 15px;
    cursor: pointer;
    border: none;
    background: none;
    font-size: 14px;
    font-weight: bold;
    color: var(--text-medium);
}

.tab.active {
    border-bottom: 2px solid var(--primary-green);
    color: var(--primary-green);
}

.form-section {
    display: none;
}

input{
    padding: 10px 30px;
}

select{
    padding: 10px 30px;
}

.form-section.active {
    display: block;
}

.donation-options {
    display: flex;
    margin-bottom: 20px;
}

.donation-type {
    flex: 1;
    padding: 15px 10px;
    text-align: center;
    background-color: var(--neutral-cream);
    cursor: pointer;
    font-weight: bold;
    color: var(--text-dark);
    margin: 0 5px;
}

.donation-type.active {
    background-color: var(--primary-green);
    color: white;
}

.donation-info {
    text-align: center;
    margin-bottom: 20px;
    font-size: 14px;
    color: var(--text-medium);
}

.currency-selector {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 20px;
}

.currency-selector label {
    margin-right: 10px;
    font-weight: bold;
    font-size: 14px;
    color: var(--text-dark);
}

.currency-selector select {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
}

.amount-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-bottom: 20px;
}

.amount-button {
    padding: 15px;
    text-align: center;
    background-color: var(--neutral-cream);
    cursor: pointer;
    font-weight: bold;
    border: none;
    color: var(--text-dark);
}

.amount-button.active {
    background-color: var(--primary-green);
    color: white;
}

.custom-amount {
    width: 100%;
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.checkbox-option {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
}

.checkbox-option input {
    margin-right: 10px;
    margin-top: 5px;
}

.checkbox-option label {
    font-size: 14px;
    color: var(--text-medium);
}

.next-button {
    width: 100%;
    padding: 15px;
    background-color: #FFA500;
    color: white;
    font-weight: bold;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-bottom: 20px;
    text-align: center;
}

.secure-checkout {
    text-align: center;
    font-size: 14px;
    color: var(--text-medium);
    margin-bottom: 10px;
}

.payment-logos {
    display: flex;
    justify-content: center;
    margin-bottom: 20px;
}

.payment-logos img {
    height: 30px;
    margin: 0 5px;
}

.help-link {
    text-align: center;
    margin-bottom: 20px;
}

.help-link a {
    color: var(--primary-green);
    text-decoration: none;
}

.footer {
    padding: 20px;
    text-align: center;
    font-size: 14px;
    color: var(--text-medium);
    border-top: 1px solid #ddd;
}

.footer a {
    color: var(--primary-green);
    text-decoration: none;
}

.video-container {
    position: relative;
    padding-bottom: 56.25%; /* 16:9 Aspect Ratio */
    height: 0;
    overflow: hidden;
    margin-bottom: 20px;
}

.video-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

/* Form Styles */
.form-title {
    color: var(--primary-green);
    font-size: 24px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input,
.form-group select {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-row {
    display: flex;
    gap: 15px;
    margin-bottom: 15px;
}

.form-col {
    flex: 1;
}

.payment-methods {
    display: flex;
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    overflow: hidden;
}

.payment-method {
    flex: 1;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    font-weight: bold;
}

.payment-method.active {
    background-color: var(--primary-green);
    color: white;
}

.payment-method:not(.active) {
    background-color: white;
    color: var(--text-dark);
}

.card-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    max-height: 20px;
}

.card-info {
    position: relative;
}

.expiry-row {
    display: flex;
    gap: 10px;
}

.expiry-col {
    flex: 1;
}

.newsletter-opt {
    background-color: #f5f5f5;
    padding: 15px;
    margin-bottom: 15px;
    border-radius: 4px;
}

@media (max-width: 768px) {
    .main-content {
        flex-direction: column;
    }
    
    .hero-image {
        height: 300px;
    }
    
    .overlay-text .number {
        font-size: 60px;
    }
    

    .form-row {
        flex-direction: column;
        gap: 15px;
    }
}