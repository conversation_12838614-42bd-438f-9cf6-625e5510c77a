footer {
  background-color: var(--primary-green);
  color: var(--text-light);
  padding: 40px 0;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
}

.footer-column h3 {
  color: var(--neutral-cream);
  margin-bottom: 20px;
  font-size: 1.2rem;
}

.footer-column ul {
  list-style: none;
}

.footer-column ul li {
  margin-bottom: 10px;
}

.footer-column a {
  color: var(--neutral-beige);
  text-decoration: none;
  transition: color 0.3s;
}

.footer-column a:hover {
  color: var(--neutral-cream);
}

.copyright {
  text-align: center;
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.9rem;
  color: var(--neutral-beige);
}

.social-icons {
  display: flex;
  gap: 15px;
}

.social-icons a {
  color: var(--neutral-beige);
  font-size: 1.2rem;
}

@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
  }
}
