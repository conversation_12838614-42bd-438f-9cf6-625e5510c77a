:root {
  --primary: #8b7355;
  --secondary: #2a4858;
  --accent: #967259;
  --light: #f6f4f0;
  --dark: #3a3026;
  --text-light: #f6f4f0;
  --text-dark: #3a3026;
}

body {
  background-color: var(--light);
  color: var(--text-dark);
  overflow-x: hidden;
  /* padding-top: 80px; */
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Comment Notification Styles */
.alert {
  padding: 15px;
  margin-bottom: 20px;
  border-radius: 4px;
  font-size: 14px;
  position: relative;
  animation: fadeIn 0.3s ease-in-out;
}

.alert-success i {
  margin-right: 8px;
  color: #28a745; /* Or any color you prefer */
}

.alert-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.alert-danger {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.alert .close {
  position: absolute;
  top: 5px;
  right: 10px;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  background: none;
  border: none;
  color: inherit;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Comment Form Error States */
.form-control.error {
  border-color: #dc3545;
}

.form-control.error:focus {
  box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 5px;
  display: none;
}

.form-control.error + .error-message {
  display: block;
}

/* Article Hero Section */
.article-hero {
  position: relative;
  height: 60vh;
  min-height: 400px;
  overflow: hidden;
}

.article-hero-image {
  width: 100%;
  height: 100%;
  background-image: url("../images/stories/mutima.jpg");
  background-size: cover;
  background-position: center;
  position: relative;
  animation: subtle-zoom 20s infinite alternate ease-in-out;
}

@keyframes subtle-zoom {
  from {
    transform: scale(1);
  }
  to {
    transform: scale(1.1);
  }
}

.article-hero-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.4),
    rgba(0, 0, 0, 0.7)
  );
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  padding: 40px;
}

.article-category {
  display: inline-block;
  background-color: var(--primary);
  color: white;
  padding: 8px 16px;
  border-radius: 30px;
  font-size: 0.9rem;
  margin-bottom: 15px;
  font-weight: 600;
  transform: translateY(30px);
  opacity: 0;
  animation: slide-up 0.8s ease forwards;
}

.article-title {
  color: white;
  font-size: 3rem;
  margin-bottom: 15px;
  line-height: 1.2;
  max-width: 800px;
  transform: translateY(30px);
  opacity: 0;
  animation: slide-up 0.8s 0.2s ease forwards;
}

.article-meta {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  transform: translateY(30px);
  opacity: 0;
  animation: slide-up 0.8s 0.4s ease forwards;
}

.author-image {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  margin-right: 15px;
  border: 2px solid rgba(255, 255, 255, 0.5);
}

.meta-divider {
  margin: 0 10px;
  color: rgba(255, 255, 255, 0.6);
}

/* Main Content */
.article-content {
  display: flex;
  margin: 40px 0;
  position: relative;
  align-items: flex-start; /* Ensures proper alignment for sticky sidebar */
  gap: 40px; /* Adds consistent spacing between main content and sidebar */
}

.main-content {
  flex: 1;
  min-width: 0; /* Prevents flex item from overflowing */
}

.article-lead {
  font-size: 1.4rem;
  line-height: 1.6;
  color: var(--secondary);
  margin-bottom: 30px;
  font-weight: 500;
  border-left: 3px solid var(--primary);
  padding-left: 20px;
  font-style: italic;
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 0.6s ease forwards;
}

.content-paragraph {
  font-size: 1.1rem;
  line-height: 1.8;
  margin-bottom: 25px;
  color: var(--text-dark);
  opacity: 0.9;
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 0.8s ease forwards;
}

.content-subheading {
  font-size: 1.8rem;
  margin: 40px 0 20px;
  color: var(--dark);
  position: relative;
  padding-bottom: 15px;
}

.content-subheading::after {
  content: "";
  position: absolute;
  width: 60px;
  height: 3px;
  background-color: var(--primary);
  bottom: 0;
  left: 0;
}

.content-image-container {
  margin: 30px 0;
  position: relative;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 1s ease forwards;
}

.content-image {
  width: 100%;
  height: auto;
  transition: transform 0.5s ease;
}

.content-image-container:hover .content-image {
  transform: scale(1.05);
}

.image-caption {
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.05);
  color: var(--text-dark);
  font-style: italic;
  text-align: center;
  font-size: 0.9rem;
}

.article-quote {
  margin: 40px 0;
  padding: 30px;
  background-color: rgba(139, 115, 85, 0.1);
  border-left: 4px solid var(--primary);
  font-size: 1.3rem;
  line-height: 1.6;
  font-style: italic;
  color: var(--dark);
  position: relative;
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 1.2s ease forwards;
}

.article-quote::before {
  content: "\f10d";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 2rem;
  color: rgba(139, 115, 85, 0.2);
  position: absolute;
  top: 20px;
  left: 20px;
}

.quote-attribution {
  margin-top: 15px;
  font-size: 1rem;
  color: var(--primary);
  font-style: normal;
  font-weight: 600;
  text-align: right;
}

.content-list {
  margin: 20px 0 30px 25px;
  line-height: 1.8;
  font-size: 1.1rem;
}

.content-list li {
  margin-bottom: 10px;
  position: relative;
  padding-left: 10px;
}

.content-list li::before {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: var(--primary);
  border-radius: 50%;
  left: -15px;
  top: 10px;
}

/* Sidebar */
.asidebar {
  width: 320px;
  position: -webkit-sticky; /* Safari */
  padding: 40px 0;
  position: sticky;
  top: 100px; /* Increased to account for header height */
  align-self: flex-start; /* Ensures sidebar aligns to top of flex container */
  max-height: calc(100vh - 100px); /* Prevents sidebar from being too tall */
  overflow-y: auto; /* Allows scrolling within sidebar if content is too long */
  scrollbar-width: thin; /* Firefox */
  scrollbar-color: var(--primary) transparent; /* Firefox */
}

/* Custom scrollbar for webkit browsers */
.asidebar::-webkit-scrollbar {
  width: 6px;
}

.asidebar::-webkit-scrollbar-track {
  background: transparent;
}

.asidebar::-webkit-scrollbar-thumb {
  background-color: var(--primary);
  border-radius: 3px;
  opacity: 0.7;
}

.asidebar::-webkit-scrollbar-thumb:hover {
  background-color: var(--accent);
}

/* Sticky state enhancement */
.asidebar.is-sticky {
  transition: box-shadow 0.3s ease;
}

.asidebar.is-sticky .asidebar-widget:first-child {
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
}

.asidebar-widget {
  background-color: white;
  border-radius: 10px;
  padding: 25px;
  margin-bottom: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 1.4s ease forwards;
}

.asidebar-widget:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.widget-title {
  font-size: 1.3rem;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid var(--primary);
  color: var(--dark);
}

.about-author {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.author-image-large {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  margin-bottom: 15px;
  border: 3px solid var(--primary);
}

.author-name {
  font-size: 1.2rem;
  margin-bottom: 5px;
  color: var(--dark);
  font-weight: 600;
}

.author-title {
  font-size: 0.9rem;
  color: var(--primary);
  margin-bottom: 15px;
}

.author-bio {
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 20px;
  color: var(--text-dark);
}

.social-links {
  display: flex;
  gap: 10px;
  margin-top: 5px;
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 35px;
  height: 35px;
  background-color: var(--light);
  border-radius: 50%;
  color: var(--primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

.social-link:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-3px);
}

.related-posts-list {
  list-style: none;
}

.related-post-item {
  display: flex;
  margin-bottom: 15px;
  padding-bottom: 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.related-post-item:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.related-post-img {
  width: 80px;
  height: 60px;
  border-radius: 5px;
  object-fit: cover;
  margin-right: 15px;
}

.related-post-content {
  flex: 1;
}

.related-post-title {
  font-size: 0.95rem;
  margin-bottom: 5px;
  line-height: 1.4;
  font-weight: 600;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.related-post-title a {
  color: var(--text-dark);
  text-decoration: none;
  transition: color 0.3s ease;
}

.related-post-title a:hover {
  color: var(--primary);
}

.related-post-date {
  font-size: 0.8rem;
  color: #777;
}

.tag-cloud {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.tag {
  padding: 6px 12px;
  background-color: var(--light);
  color: var(--primary);
  border-radius: 20px;
  font-size: 0.8rem;
  text-decoration: none;
  transition: all 0.3s ease;
}

.tag:hover {
  background-color: var(--primary);
  color: white;
  transform: translateY(-3px);
}

/* Gallery Section */
.photo-gallery {
  margin: 50px 0;
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 1.6s ease forwards;
}

.gallery-title {
  font-size: 1.8rem;
  margin-bottom: 30px;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.gallery-title::after {
  content: "";
  position: absolute;
  width: 80px;
  height: 3px;
  background-color: var(--primary);
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15px;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  height: 200px;
}

.gallery-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-item:hover .gallery-image {
  transform: scale(1.1);
}

.gallery-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.gallery-item:hover .gallery-overlay {
  opacity: 1;
}

.gallery-icon {
  color: white;
  font-size: 1.5rem;
  transform: scale(0);
  transition: transform 0.3s ease;
}

.gallery-item:hover .gallery-icon {
  transform: scale(1);
}

/* Share and Comments */
.article-engagement {
  padding: 3.5rem;
  margin-top: 40px;
  padding-top: 40px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 1.8s ease forwards;
}

.share-section {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
}

.share-label {
  margin-right: 15px;
  font-weight: 600;
  color: var(--dark);
}

.share-buttons {
  display: flex;
  gap: 10px;
}

.share-button {
  display: flex;
  align-items: center;
  padding: 8px 15px;
  border-radius: 5px;
  color: white;
  text-decoration: none;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.share-button i {
  margin-right: 8px;
}

.share-button.facebook {
  background-color: #3b5998;
}

.share-button.twitter {
  background-color: #1da1f2;
}

.share-button.linkedin {
  background-color: #0077b5;
}

.share-button.pinterest {
  background-color: #bd081c;
}

.share-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* Comments */
.comments-section {
  margin-top: 40px;
}

.comments-title {
  font-size: 1.5rem;
  margin-bottom: 30px;
  color: var(--dark);
}

.comment {
  display: flex;
  margin-bottom: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.comment:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 20px;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.comment-name {
  font-weight: 600;
  color: var(--dark);
}

.comment-date {
  color: #777;
  font-size: 0.9rem;
}

.comment-text {
  line-height: 1.6;
  margin-bottom: 15px;
  color: var(--text-dark);
}

.reply-button {
  color: var(--primary);
  font-weight: 600;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  transition: color 0.3s ease;
}

.reply-button i {
  margin-right: 5px;
  font-size: 0.8rem;
}

.reply-button:hover {
  color: var(--accent);
}

.nested-comment {
  margin-left: 80px;
  margin-top: 30px;
  padding-top: 30px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* Comment Form */
.comment-form {
  margin-top: 50px;
  background-color: white;
  padding: 30px;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 2s ease forwards;
}

.form-title {
  font-size: 1.5rem;
  margin-bottom: 20px;
  color: var(--dark);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  flex: 1;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: var(--dark);
}

.form-control {
  width: 100%;
  padding: 12px 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
  transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(139, 115, 85, 0.2);
}

textarea.form-control {
  min-height: 150px;
  resize: vertical;
}

.submit-btn {
  padding: 12px 25px;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 600;
}

.submit-btn:hover {
  background-color: var(--accent);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Related Articles Carousel */
.related-articles {
  margin: 60px 0;
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 2.2s ease forwards;
}

.related-title {
  font-size: 2rem;
  margin-bottom: 30px;
  text-align: center;
  position: relative;
  padding-bottom: 15px;
}

.related-title::after {
  content: "";
  position: absolute;
  width: 80px;
  height: 3px;
  background-color: var(--primary);
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
}

.carousel-container {
  position: relative;
  padding: 0 50px;
}

.carousel-track {
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  padding: 20px 0;
  gap: 20px;
}

.carousel-track::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

.carousel-slide {
  min-width: 300px;
  background-color: white;
  border-radius: 10px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.carousel-slide:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.carousel-image {
  height: 180px;
  width: 100%;
  object-fit: cover;
}

.carousel-content {
  padding: 20px;
}

.carousel-category {
  display: inline-block;
  background-color: rgba(139, 115, 85, 0.1);
  color: var(--primary);
  padding: 5px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.carousel-title {
  font-size: 1.1rem;
  margin-bottom: 10px;
  line-height: 1.4;
  color: var(--dark);
}

.carousel-title a {
  color: inherit;
  text-decoration: none;
  transition: color 0.3s ease;
}

.carousel-title a:hover {
  color: var(--primary);
}

.carousel-date {
  color: #777;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
}

.carousel-date i {
  margin-right: 5px;
  font-size: 0.8rem;
}

.carousel-nav {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  background-color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--dark);
  font-size: 1.2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
}

.carousel-nav:hover {
  background-color: var(--primary);
  color: white;
}

.carousel-prev {
  left: 0;
}

.carousel-next {
  right: 0;
}

/* Call to Action */
.cta-section {
  padding: 60px 40px;
  border-radius: 10px;
  margin: 60px 0;
  color: white;
  text-align: center;
  background-image: linear-gradient(
      135deg,
      rgba(42, 72, 88, 0.9),
      rgba(42, 72, 88, 0.7)
    ),
    url("../images/hero/hejuru.jpeg");
  background-size: cover;
  background-position: center;
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
  transform: translateY(20px);
  opacity: 0;
  animation: slide-up 0.8s 2.4s ease forwards;
}

.cta-title {
  font-size: 2.5rem;
  margin-bottom: 20px;
}

.cta-text {
  font-size: 1.1rem;
  margin-bottom: 30px;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  line-height: 1.6;
  opacity: 0.9;
}

.cta-btn {
  display: inline-block;
  padding: 15px 30px;
  background-color: var(--primary);
  color: white;
  text-decoration: none;
  border-radius: 5px;
  font-weight: 600;
  transition: all 0.3s ease;
  font-size: 1.1rem;
}

.cta-btn:hover {
  background-color: var(--accent);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Animations */
@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Back to top button */
.back-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background-color: var(--primary);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  font-size: 1.2rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 999;
}

.back-to-top.visible {
  opacity: 1;
}

.back-to-top:hover {
  background-color: var(--accent);
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

/* Responsive Styles */
@media (max-width: 992px) {
  .article-title {
    font-size: 2.5rem;
  }

  .article-content {
    flex-direction: column;
    gap: 30px;
  }

  .main-content {
    margin-bottom: 40px;
  }

  .asidebar {
    width: 100%;
    position: static; /* Remove sticky behavior on smaller screens */
    max-height: none;
    overflow-y: visible;
  }

  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .article-hero {
    height: 50vh;
  }

  .article-title {
    font-size: 2rem;
  }

  .article-lead {
    font-size: 1.2rem;
  }

  .content-paragraph {
    font-size: 1rem;
  }

  .article-quote {
    font-size: 1.1rem;
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .gallery-grid {
    grid-template-columns: 1fr;
  }

  .share-section {
    flex-direction: column;
    align-items: flex-start;
  }

  .share-label {
    margin-bottom: 10px;
  }

  .cta-title {
    font-size: 2rem;
  }
  
  .share-buttons {
      flex-direction: column;
  }
}

@media (max-width: 576px) {
  .article-hero {
    height: 40vh;
  }

  .article-title {
    font-size: 1.8rem;
  }

  .article-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .meta-divider {
    display: none;
  }

  .content-image-container {
    margin: 20px 0;
  }

  .article-quote {
    font-size: 1.1rem;
    padding: 15px;
  }

  .cta-title {
    font-size: 1.5rem;
  }

  .cta-text {
    font-size: 1rem;
  }
}
