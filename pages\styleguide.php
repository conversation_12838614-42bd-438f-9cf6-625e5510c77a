<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rwanda Gorilla Trekking | Volcanoes National Park Tours | Virunga Ecotours</title>
<meta name="description" content="Book authentic mountain gorilla trekking in Rwanda's Volcanoes National Park. Community-based tours with local guides offering cultural immersion and sustainable tourism experiences.">
<meta name="keywords" content="Rwanda gorilla trekking, Volcanoes National Park, mountain gorilla tours, Rwanda wildlife, sustainable gorilla tourism, Parc National des Volcans">
    <title>East Africa Travel Style Guides - Virunga Ecotours</title>
    <link rel="shortcut icon" href="../images/logos/icon.png" type="image/x-icon">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css">
    <link rel="stylesheet" href="../css/earthy-theme.css">
    <link rel="stylesheet" href="../css/header.css">
    <script src="../js/header.js" defer></script>
    <style>

        body {
            max-width: 100%;
            overflow-x: hidden;
            background-color: var(--neutral-light);
            color: var(--text-dark);
        }

        .hero-banner {
            width: 100%;
            height: 60vh;
            background-image: linear-gradient(rgba(42, 72, 88, 0.6), rgba(42, 72, 88, 0.6)),
            url('../images/hero/cover6.JPG');
            background-size: cover;
            background-position: center;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: var(--text-light);
            text-align: center;
            position: relative;
            margin-top: 0;
        }

        .hero-title {
            font-size: 52px;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.7);
            max-width: 800px;
            line-height: 1.2;
        }

        .hero-subtitle {
            font-size: 20px;
            margin-bottom: 30px;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            max-width: 600px;
            line-height: 1.4;
        }

        .rating-box {
            background-color: var(--neutral-cream);
            border-radius: 8px;
            padding: 8px 16px;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin-top: 10px;
        }

        .rating-row {
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 5px;
        }

        .rating-icons {
            display: flex;
            gap: 5px;
        }

        .rating-icon {
            width: 20px;
            height: 20px;
            border-radius: 50%;
        }

        .icon-green {
            background-color: var(--primary-green);
        }

        .icon-brown {
            background-color: var(--primary-brown);
        }

        .icon-sage {
            background-color: var(--accent-sage);
        }

        .rating-stars {
            color: var(--accent-terracotta);
            font-size: 18px;
        }

        .rating-number {
            font-size: 18px;
            font-weight: bold;
            margin-right: 5px;
        }

        .rating-text {
            font-size: 12px;
            color: var(--text-medium);
        }

        .main-content {
            padding: 40px 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .intro-section {
            margin-bottom: 40px;
        }

        .intro-title {
            font-size: 32px;
            margin-bottom: 25px;
            font-weight: bold;
            color: var(--primary-green);
            text-align: center;
        }

        .intro-columns {
            display: flex;
            gap: 40px;
            margin-top: 30px;
        }

        .intro-column {
            flex: 1;
            font-size: 16px;
            line-height: 1.7;
        }

        .text-link {
            color: var(--accent-terracotta);
            text-decoration: none;
        }

        .text-link:hover {
            text-decoration: underline;
        }

        .guides-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 50px;
        }

        .guide-card {
            position: relative;
            height: 280px;
            overflow: hidden;
            border-radius: 12px;
            margin-bottom: 20px;
            text-decoration: none;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .guide-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .guide-card img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .guide-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 20px;
            background: linear-gradient(transparent, rgba(42, 72, 88, 0.7));
            color: var(--text-light);
        }

        .guide-title {
            font-size: 20px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
            margin-bottom: 8px;
        }

        .guide-description {
            font-size: 14px;
            opacity: 0.9;
            line-height: 1.4;
        }

        p {
            margin-bottom: 16px;
            line-height: 1.6;
        }

        .stats-section {
            background-color: var(--neutral-cream);
            padding: 40px 20px;
            margin: 60px 0;
            border-radius: 12px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            max-width: 1000px;
            margin: 0 auto;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 36px;
            font-weight: bold;
            color: var(--primary-green);
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 16px;
            color: var(--text-medium);
            font-weight: 500;
        }

        @media (max-width: 900px) {
            .guides-grid {
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }
            .intro-columns {
                flex-direction: column;
                gap: 20px;
            }
        }

        /* Empty and Error States */
        .empty-state, .error-state {
            grid-column: 1 / -1;
            text-align: center;
            padding: 60px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .empty-state i, .error-state i {
            font-size: 4rem;
            margin-bottom: 1.5rem;
            color: var(--neutral-beige);
        }

        .error-state i {
            color: var(--accent-terracotta);
        }

        .empty-state h3, .error-state h3 {
            margin-bottom: 1rem;
            color: var(--text-dark);
            font-size: 1.5rem;
        }

        .empty-state p, .error-state p {
            margin-bottom: 2rem;
            color: var(--text-medium);
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .btn-primary, .btn-secondary {
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 1.5rem;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .btn-primary {
            background: var(--primary-green);
            color: white;
        }

        .btn-primary:hover {
            background: var(--accent-sage);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: var(--neutral-beige);
            color: var(--text-dark);
        }

        .btn-secondary:hover {
            background: var(--accent-terracotta);
            color: white;
        }

        @media (max-width: 600px) {
            .guides-grid {
                grid-template-columns: 1fr;
            }
            .hero-title {
                font-size: 36px;
            }
            .hero-subtitle {
                font-size: 18px;
            }
            .guide-card {
                height: 250px;
            }
            .empty-state, .error-state {
                padding: 40px 20px;
            }
        }
    </style>
</head>
<body>
    <?php include('./includes/header.php'); ?>

    <div class="hero-banner">
        <h1 class="hero-title">East Africa Travel Style Guides</h1>
        <p class="hero-subtitle">Discover Rwanda, Uganda & DR Congo through personalized travel experiences</p>
        <div class="rating-box">
            <div class="rating-row">
                <div class="rating-icons">
                    <span class="rating-icon icon-green"></span>
                    <span class="rating-icon icon-brown"></span>
                    <span class="rating-icon icon-sage"></span>
                </div>
            </div>
            <div class="rating-row">
                <span class="rating-number">4.9</span>
                <span class="rating-stars">★★★★★</span>
            </div>
            <div class="rating-text">Read our 240+ reviews</div>
        </div>
    </div>

    <div class="main-content">
        <section class="intro-section">
            <h2 class="intro-title">Discover East Africa Through Your Travel Style</h2>
            <div class="intro-columns">
                <div class="intro-column">
                    <p>Every traveler is unique, and so should be their journey. <a href="./build.php" class="text-link">Virunga Ecotours categorizes all tours and experiences by Travel Style</a> to help you find the perfect adventure across Rwanda, Uganda, and DR Congo. Whether you seek thrilling gorilla encounters, cultural immersion, or peaceful nature retreats, our style guides are crafted just for you.</p>
                    <p>We recommend destinations, activities, and tours for each Travel Style to ensure you experience the authentic beauty and wonder of East Africa in a way that resonates with your personal travel preferences.</p>
                </div>
                <div class="intro-column">
                    <p>Our East Africa Travel Style Guides are designed to inspire and inform. If you don't find a perfect match or if multiple styles appeal to you, connect with our team of <a href="./contactus.php" class="text-link">expert local guides and travel designers</a>. We'll craft a completely customized itinerary that captures your travel dreams while supporting local communities and conservation efforts.</p>
                    <p><strong>Explore responsibly. Travel sustainably. Create lasting memories.</strong></p>
                </div>
            </div>
        </section>

        <!-- Statistics Section -->
        <section class="stats-section">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">3</div>
                    <div class="stat-label">Countries Covered</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">10+</div>
                    <div class="stat-label">Unique Experiences</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">8+</div>
                    <div class="stat-label">Years of Expertise</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100+</div>
                    <div class="stat-label">Happy Travelers</div>
                </div>
            </div>
        </section>

        <div class="guides-grid">
            <?php
            require_once('../admin/config/database.php');

            try {
                // Fetch style guides with content preview
                $query = "
                    SELECT
                        sg.card_id,
                        sg.title,
                        sg.thumbnail_image,
                        sg.created_at,
                        sc.intro_text
                    FROM styleguide_cards sg
                    LEFT JOIN styleguide_content sc ON sg.card_id = sc.card_id
                    ORDER BY sg.created_at DESC
                ";
                $stmt = $pdo->prepare($query);
                $stmt->execute();
                $guides = $stmt->fetchAll(PDO::FETCH_ASSOC);

                if ($guides && count($guides) > 0) {
                    foreach ($guides as $guide) {
                        $thumbnail = $guide['thumbnail_image'] ? '../admin/images/style-guide/' . $guide['thumbnail_image'] : '../images/default-guide.jpg';
                        $description = $guide['intro_text'] ? substr(strip_tags($guide['intro_text']), 0, 100) . '...' : 'Discover unique experiences and hidden gems in East Africa';

                        echo '
                        <a href="styleguideopen.php?id=' . $guide['card_id'] . '" class="guide-card">
                            <img src="' . htmlspecialchars($thumbnail) . '" alt="' . htmlspecialchars($guide['title']) . '" onerror="this.src=\'../images/default-guide.jpg\'">
                            <div class="guide-overlay">
                                <h3 class="guide-title">' . htmlspecialchars($guide['title']) . '</h3>
                                <p class="guide-description">' . htmlspecialchars($description) . '</p>
                            </div>
                        </a>';
                    }
                } else {
                    echo '
                    <div class="empty-state">
                        <i class="fas fa-book-open"></i>
                        <h3>No Style Guides Available</h3>
                        <p>We\'re working on creating amazing travel style guides for you. Please check back soon!</p>
                        <a href="../index.php" class="btn-primary">
                            <i class="fas fa-home"></i> Return Home
                        </a>
                    </div>';
                }
            } catch(PDOException $e) {
                echo '
                <div class="error-state">
                    <i class="fas fa-exclamation-triangle"></i>
                    <h3>Error Loading Style Guides</h3>
                    <p>We\'re experiencing technical difficulties. Please try again later.</p>
                    <button onclick="location.reload()" class="btn-secondary">
                        <i class="fas fa-refresh"></i> Try Again
                    </button>
                </div>';
            }
            ?>
        </div>
    </div>

    <?php include('./includes/footer.php'); ?>
</body>
</html>