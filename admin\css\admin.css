/* Add these styles to your existing admin.css */
.messages-container {
    margin-top: 2rem;
}

.message-card {
    background: white;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border-left: 4px solid #ddd;
}

.message-card.unread {
    border-left-color: #4CAF50;
    background-color: #f8f9fa;
}

.message-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.5rem;
}

.message-date {
    color: #666;
    font-size: 0.9rem;
}

.message-sender {
    margin-bottom: 1rem;
    color: #555;
}

.sender-email {
    color: #2196F3;
    margin-left: 1rem;
}

.message-content {
    line-height: 1.6;
    margin-bottom: 1rem;
    white-space: pre-wrap;
}

.message-actions {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
}

.inline-form {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 0.8rem;
}

.inline-form textarea {
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    min-width: 300px;
    resize: vertical;
    font-family: inherit;
}

.inline-form button {
    padding: 8px 16px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.inline-form button:hover {
    background-color: #45a049;
}

.inline-form button.btn-primary {
    background-color: #2196F3;
}

.inline-form button.btn-primary:hover {
    background-color: #0b7dda;
}

.response-info {
    background: #f5f5f5;
    padding: 0.8rem;
    border-radius: 4px;
    margin-top: 0.5rem;
}

.btn-sm {
    padding: 0.3rem 0.8rem;
    font-size: 0.9rem;
}