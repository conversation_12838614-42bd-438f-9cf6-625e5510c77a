.filter-container {
  margin-bottom: 2rem;
  padding: 1rem 0;
}

.filter-categories {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  justify-content: center;
  margin: 0 auto;
  max-width: 1200px;
}

.filter-btn {
  padding: 0.8rem 1.5rem;
  border: 2px solid var(--primary-brown);
  background: transparent;
  color: var(--primary-brown);
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.9rem;
  outline: none;
  min-width: 100px;
  position: relative;
  overflow: hidden;
}

.filter-btn:hover {
  background: var(--primary-brown);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.filter-btn:active {
  transform: translateY(0);
}

.filter-btn.active {
  background: var(--primary-brown);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.holiday-card {
  transition: all 0.3s ease;
  opacity: 1;
}

.holiday-card[style*="display: none"] {
  opacity: 0;
  transform: scale(0.95);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .filter-categories {
    gap: 0.5rem;
  }

  .filter-btn {
    padding: 0.6rem 1rem;
    font-size: 0.8rem;
    min-width: 80px;
  }
}

.show-more-container {
  text-align: center;
  margin-top: 2rem;
}

.show-more-btn {
  padding: 0.8rem 2rem;
  background-color: var(--primary-brown);
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.show-more-btn:hover {
  background-color: var(--secondary-brown);
  transform: translateY(-2px);
}

.show-more-btn:active {
  transform: translateY(0);
}
