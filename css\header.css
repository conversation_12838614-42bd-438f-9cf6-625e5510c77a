* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: Arial, sans-serif;
}

body {
  overflow-x: hidden;
}

a {
  text-decoration: none;
  color: inherit;
}

ul {
  list-style: none;
}

/* Top Bar */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 15px;
  background-color: var(--neutral-light);
}


.trustpilot {
  display: flex;
  align-items: center;
}

.trustpilot a {
  margin-right: 10px;
  font-size: 14px;
  color: var(--text-medium);
  font-weight: bold;
}

/* TripAdvisor styles */
.separator {
  margin: 0 10px;
}

.tripadvisor-link {
  display: flex;
  align-items: center;
}


.tripadvisor-mobile, 
.tripadvisor-desktop {
  height: 50px;
  width: 90px;
}

.tripadvisor-mobile {
  display: none;
}

.tripadvisor-desktop {
  display: inline-block;
}

@media (max-width: 768px) {
  .tripadvisor-mobile {
    display: inline-block;
  }
  
  .tripadvisor-desktop {
    display: none;
  }
}


.top-nav {
  display: flex;
}

.top-nav a {
  margin-left: 15px;
  text-transform: uppercase;
  font-size: 12px;
  color: var(--text-medium);
  font-weight: bold;
}

.top-nav a:hover {
  color: var(--accent-terracotta);
}

/* Header */

.header {
  background-color: var(--primary-green);
  color: var(--text-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 30px;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 2px 5px rgba(58, 48, 38, 0.15);
}

.logo {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo img {
  max-width: 200px;
  margin: auto;
  padding: 0;
  height: 40px;
}


.contact-info {
  display: flex;
  align-items: center;
}

.nav-phone-number {
  font-size: 22px;
  margin-right: 10px;
  text-align: end;
}

.nav-phone-number span {
  font-size: 12px;
}

.closed {
  text-align: right;
  font-size: 12px;
  margin-right: 15px;
}

.quote-btn {
  text-transform: uppercase;
  background-color: var(--neutral-cream);
  color: var(--primary-green);
  padding: 12px 20px;
  font-weight: bold;
  border: 2px solid var(--neutral-cream);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
}

.quote-btn:hover {
  background-color: transparent;
  color: var(--neutral-cream);
}

/* Main Navigation */
.main-nav {
  background-color: var(--neutral-light);
  display: flex;
  position: sticky;
  top: 4rem;
  z-index: 100;
  padding-left: 2rem;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.nav-item {
  position: relative;
  padding: 15px 20px;
  font-weight: bold;
  text-transform: capitalize;
  font-size: 12px;
  color: var(--text-medium);
  cursor: pointer;
  border-bottom: 3px solid transparent;
  transition: border-color 0.3s ease;
}

.nav-item span {
  text-transform: uppercase;
}

.nav-item i {
  margin-left: 5px;
  transition: all 0.3s ease;
  font-size: 10px;
  font-weight: 700;
  color: var(--accent-terracotta);
}

.nav-item:hover i {
  rotate: 180deg;
}

.search-btn {
  background-color: var(--accent-terracotta);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 0 20px;
  cursor: pointer;
  margin-left: auto;
  transition: background-color 0.3s ease;
}

.search-btn:hover {
  background-color: var(--accent-light-brown);
}

/* Dropdown Menu */
.dropdown {
  position: absolute;
  opacity: 0;
  visibility: hidden;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  z-index: 100;
  left: 0;
  top: 100%;
  min-width: 200px;
  transition: all 0.3s ease;
  background-color: var(--neutral-light);
  border-top: 3px solid var(--accent-terracotta);
  transform: translateY(-5px);
}

.nav-item:hover .dropdown {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.dropdown-container {
  padding: 8px 0;
}

.destination-group {
  display: flex;
  flex-direction: column;
}

.destination-group a {
  color: var(--text-medium);
  font-size: 12px;
  padding: 10px 20px;
  transition: all 0.3s ease;
  display: block;
  text-transform: uppercase;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  
}

.destination-group a:last-child {
  border-bottom: none;
}

.destination-group a:hover {
  color: var(--accent-terracotta);
  background-color: rgba(0, 0, 0, 0.03);
}

/* About Us Dropdown Specific Styles */
.about-nav .dropdown {
  width: 900px;
  left: 450px;
  transform: translateX(-50%) translateY(-5px);
  margin-left: 2rem;
}

.about-nav:hover .dropdown {
  transform: translateX(-50%) translateY(0);
}

.about-container {
  display: flex;
  max-width: 900px;
  padding: 20px;
}

.about-section {
  display: flex;
  width: 100%;
  gap: 30px;
}

.about-links {
  min-width: 250px;
}

.about-links h2 {
  color: var(--text-medium);
  font-size: 14px;
  margin-bottom: 15px;
}

.about-links ul {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.about-links ul li a {
  color: var(--text-medium);
  font-size: 12px;
  transition: color 0.3s ease;
}

.about-links ul li a:hover {
  color: var(--accent-terracotta);
}

.featured-section {
  flex: 1;
}

.featured-section h2 {
  color: var(--text-medium);
  font-size: 14px;
  margin-bottom: 15px;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.featured-item {
  text-align: left;
}

.featured-image {
  margin-bottom: 10px;
  overflow: hidden;
}

.featured-image img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.featured-item:hover img {
  transform: scale(1.05);
}

.featured-item h3 {
  color: var(--text-medium);
  font-size: 14px;
  margin-bottom: 8px;
}

.featured-item p {
  color: var(--text-light-medium);
  font-size: 12px;
  line-height: 1.5;
}

/* Regions Section */
.regions-section {
  display: flex;
  background-color: #f5f5f5;
}

.sidebar {
  width: 25%;
  background-color: white;
  padding: 30px;
  border-right: 1px solid #eee;
}

.sidebar h3 {
  font-size: 14px;
  margin-bottom: 20px;
  color: #333;
}

.sidebar ul li {
  padding: 10px 0;
  font-size: 12px;
  font-weight: 400;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 10px;
}

.sidebar ul li:hover {
  color: var(--accent-terracotta);
  background-color: rgba(165, 28, 69, 0.05);
}

.sidebar ul li.active-region {
  color: var(--accent-terracotta);
  font-weight: 600;
  background-color: rgba(165, 28, 69, 0.1);
  border-left: 3px solid var(--accent-terracotta);
  padding-left: 7px;
}

.destinations {
  width: 50%;
  padding: 30px;
}

.destinations h3 {
  font-size: 14px;
  margin-bottom: 20px;
  color: #333;
}

.destinations-grid {
  display: block;
}

.nav-destination-item {
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 4px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}

.nav-destination-item:hover, .destination-hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.nav-destination-item a {
  color: #001a33;
  font-size: 12px;
  font-weight: 600;
  padding: 12px;
  text-align: center;
}

.nav-destination-item a:hover {
  color: var(--accent-terracotta);
}

.featured {
  width: 25%;
  padding: 30px;
}

.featured h3 {
  font-size: 14px;
  margin-bottom: 20px;
  color: #333;
}

.featured-img {
  width: 100%;
  height: 120px;
  background-color: #ddd;
  margin-bottom: 15px;
  background-image: url("./app.png");
  background-size: cover;
  background-position: center;
}

.featured h4 {
  font-size: 12px;
  margin-bottom: 10px;
  color: #333;
}

.featured p {
  font-size: 12px;
  font-weight: 400;
  color: #666;
  line-height: 1.5;
}

#region-featured-img {
  transition: opacity 0.3s ease;
}

#region-featured-title,
#region-featured-description {
  transition: opacity 0.3s ease;
}

@media (max-width: 992px) {
  .destination-group {
    flex-direction: column;
  }
}

@media (max-width: 576px) {
  .destination-group {
    flex-direction: column;
  }
}

/* Side Buttons */
.side-buttons {
  position: fixed;
  right: 0;
  top: 50.6%;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  z-index: 999;
  transition: all 0.3s ease;
}

.side-btn {
  background-color: var(--accent-terracotta);
  color: white;
  width: 90px;
  height: 90px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.side-btn i {
  font-size: 24px;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

.side-btn span {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Compact side buttons for scroll state */
.side-buttons.compact .side-btn {
  width: 45px;
  height: 45px;
  margin-bottom: 5px;
  border-radius: 4px 0 0 4px;
}

.side-buttons.compact .side-btn i {
  font-size: 16px;
  margin-bottom: 0;
}

.side-buttons.compact .side-btn span {
  opacity: 0;
  transform: translateY(10px);
  position: absolute;
}

.tailor-made-section {
  width: 30%;
  padding: 30px;
  border-right: 1px solid #e0e0e0;
  background-color: #fff;
}

.tailor-made-section h2 {
  color: #001a33;
  font-size: 14px;
  margin-bottom: 25px;
}

.tailor-made-section ul {
  list-style: none;
}

.tailor-made-section ul li {
  margin-bottom: 12px;
}

.tailor-made-section ul li a {
  color: #001a33;
  text-decoration: none;
  font-size: 12px;
  font-weight: 400;
  display: block;
  padding: 4px 0;
  transition: color 0.3s;
}

.tailor-made-section ul li a:hover {
  color: var(--accent-terracotta);
}

.featured-section {
  background-color: #fff;
  width: 70%;
  padding: 30px;
}

.featured-section h2 {
  color: #001a33;
  font-size: 18px;
  margin-bottom: 30px;
  font-weight: bold;
}

.featured-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
}

.featured-item {
  display: flex;
  flex-direction: column;
}

.featured-image {
  height: 160px;
  width: 100%;
  margin-bottom: 15px;
  overflow: hidden;
  position: relative;
}

.featured-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  transition: transform 0.3s ease, opacity 0.3s ease;
}

.featured-item:hover .featured-image img {
  transform: scale(1.05);
}

.featured-item h3 {
  color: #001a33;
  font-size: 12px;
  margin-bottom: 10px;
  font-weight: bold;
  text-transform: uppercase;
}

.featured-item p {
  color: #333;
  font-size: 12px;
  line-height: 1.6;
  font-weight: 400;
}

@media (max-width: 768px) {
  .dropdown-container {
    flex-direction: column;
  }

  .tailor-made-section,
  .featured-section {
    width: 100%;
  }

  .featured-grid {
    grid-template-columns: 1fr;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .contact-info {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    cursor: pointer;
    z-index: 1000;
  }

  .mobile-menu-toggle span {
    display: block;
    width: 100%;
    height: 2px;
    background-color: #fff;
    transition: all 0.3s ease;
  }

  .mobile-nav {
    position: fixed;
    top: 0;
    right: -100%;
    width: 85%;
    height: 100vh;
    background-color: #fff;
    z-index: 999;
    overflow-y: auto;
    transition: right 0.3s ease;
  }

  .mobile-nav.active {
    right: 0;
  }

  .mobile-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 998;
  }

  .mobile-overlay.active {
    display: block;
  }

  .mobile-nav-header {
    display: flex;
    justify-content: flex-end;
    padding: 15px;
    background-color: var(--primary-green);
  }

  .mobile-nav-header .mobile-close i {
    color: #fff;
    font-size: 24px;
  }

  .mobile-close {
    color: #fff;
    font-size: 24px;
    cursor: pointer;
  }

  .mobile-nav-content {
    padding: 20px;
  }

  .mobile-top-links {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
  }

  .mobile-top-links a {
    color: var(--text-dark);
    font-size: 14px;
    text-transform: uppercase;
  }
  
  .mobile-top-links .mobile-tripadvisor-link {
    display: flex;
    align-items: center;
    gap: 8px;
  }
  
  .mobile-top-links .mobile-tripadvisor-link img {
    height: 20px;
    width: auto;
  }

  .mobile-main-links .mobile-nav-item {
    margin-bottom: 15px;
  }

  .mobile-nav-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    color: var(--text-dark);
    font-weight: bold;
  }

  .mobile-nav-content ul {
    padding-left: 15px;
    display: none;
  }

  .mobile-nav-content ul.active {
    display: block;
  }

  .mobile-nav-content ul li {
    margin: 10px 0;
  }

  .mobile-nav-content ul li a {
    color: var(--text-medium);
    font-size: 14px;
  }
}

@media (max-width: 768px) {
  .top-bar {
    display: none;
  }
  
  .main-nav {
    display: none;
  }
  
  .side-buttons {
    display: none;
  }
  
  .mobile-side-buttons {
    display: grid;
  }
  
  .header {
    position: fixed;
    width: 100%;
    top: 0;
  }
  
  .logo img {
    max-width: 100px;
    height: 15px;
  }
  
  /* Add spacing for fixed header and mobile side buttons */
  body {
    padding-top: 110px; /* 60px header + 50px mobile buttons */
  }
  
  .hero-section {
    margin-top: 0;
  }
}

@media (max-width: 768px) {
  .top-bar {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .top-nav {
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px;
  }

  .top-nav a {
    margin-left: 0;
  }

  .header {
    padding: 5px 15px;
    flex-wrap: wrap;
  }

  .header .logo {
    margin: 0 10px;
  }

  .header .main-nav {
    width: 100%;
    order: 3;
  }

  .header .contact-info {
    display: none;
  }

  .logo img {
    height: 40px;
  }

  .nav-phone-number {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .top-bar {
    padding: 10px;
  }

  .trustpilot {
    flex-direction: column;
    gap: 5px;
  }

  .trustpilot span {
    margin-right: 0;
  }

  .logo img {
    height: 35px;
  }
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  cursor: pointer;
  z-index: 1000;
}

.mobile-menu-toggle span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: white;
  border-radius: 4px;
  transition: all 0.3s ease;
}

/* Mobile Navigation */
.mobile-nav {
  position: fixed;
  top: 0;
  right: -300px;
  width: 300px;
  height: 100%;
  background-color: #fff;
  z-index: 1001;
  overflow-y: auto;
  transition: right 0.3s ease;
  box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
}

.mobile-nav.active {
  right: 0;
}

.mobile-nav-header {
  display: flex;
  justify-content: flex-end;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.mobile-close {
  font-size: 24px;
  cursor: pointer;
  color: #001a33;
}

.mobile-nav-content {
  padding: 20px 0;
}

.mobile-top-links {
  display: flex;
  flex-direction: column;
  padding: 0 20px 20px;
  border-bottom: 1px solid #eee;
}

.mobile-top-links a {
  color: #001a33;
  text-decoration: none;
  padding: 10px 0;
  font-size: 14px;
}

.mobile-main-links {
  padding: 20px 0;
}

.mobile-nav-item {
  border-bottom: 1px solid #eee;
}

.mobile-nav-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  cursor: pointer;
  font-weight: bold;
  color: #001a33;
}

.mobile-nav-title span a {
  color: #001a33;
  text-decoration: none;
}

.mobile-dropdown {
  display: none;
  padding: 0 20px 15px;
  background-color: #f9f9f9;
}

.mobile-dropdown.active {
  display: block;
}

.mobile-regions h4 {
  font-size: 14px;
  color: var(--accent-terracotta);
  margin: 15px 0 10px;
}

.mobile-regions ul,
.mobile-links-list ul {
  display: block;
  list-style: none;
  padding: 0;
  margin: 0;
}

.mobile-regions li,
.mobile-links-list li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
  font-size: 14px;
}

.mobile-links-list li a {
  color: #001a33;
  text-decoration: none;
  display: block;
}

.mobile-links-list li:last-child,
.mobile-regions li:last-child {
  border-bottom: none;
}

/* Mobile Side Buttons */
.mobile-side-buttons {
  display: none;
  background-color: #fff;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 60px;
  left: 0;
  width: 100%;
  z-index: 999;
  grid-template-columns: repeat(3, 1fr);
  text-align: center;
}

.mobile-side-btn {
  padding: 12px 5px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #001a33;
  font-size: 12px;
  font-weight: bold;
  border-right: 1px solid #eee;
}

.mobile-side-btn:last-child {
  border-right: none;
}

.mobile-side-btn i {
  font-size: 18px;
  margin-bottom: 5px;
  color: var(--accent-terracotta);
}

.mobile-side-btn span {
  text-transform: uppercase;
  font-size: 10px;
}

/* Responsive styles */
@media (max-width: 992px) {
  .contact-info {
    display: none;
  }
  
  .mobile-menu-toggle {
    display: flex;
  }
  
  .header {
    justify-content: space-between;
    padding: 15px 20px;
  }
}

@media (max-width: 768px) {
  .top-bar {
    display: none;
  }
  
  .main-nav {
    display: none;
  }
  
  .side-buttons {
    display: none;
  }
  
  .mobile-side-buttons {
    display: grid;
  }
  
  .header {
    position: fixed;
    width: 100%;
    top: 0;
  }
  
  .logo img {
    max-width: 150px;
    height: 30px;
  }
  
  /* Add spacing for fixed header and mobile side buttons */
  body {
    padding-top: 110px; /* 60px header + 50px mobile buttons */
  }
  
  .hero-section {
    margin-top: 0;
  }
}

/* Overlay for mobile menu */
.mobile-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.mobile-overlay.active {
  display: block;
}

/* Modern Footer Styles */
.site-footer {
  background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.8)), url("../images/15.jpg") no-repeat center/cover;
  color: #fff;
  padding: 0;
  overflow: hidden;
  position: relative;
}

.site-footer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at top right, rgba(0, 40, 80, 0.4) 0%, rgba(0, 26, 51, 0) 70%);
  pointer-events: none;
}

.footer-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.footer-top {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1.5fr;
  gap: 40px;
  padding: 60px 0 40px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.footer-column h3 {
  font-size: 14px;
  font-weight: 700;
  margin-bottom: 25px;
  letter-spacing: 1px;
  position: relative;
  display: inline-block;
  color: var(--neutral-light);
}

.footer-column h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: var(--accent-terracotta);
  transition: width 0.4s ease;
}

.footer-column:hover h3::after {
  width: 100%;
}

/* Company Info Column */
.company-info {
  animation: fadeInUp 0.6s ease-out forwards;
}

.footer-links {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px 20px;
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  transition: all 0.3s ease;
  position: relative;
  padding-left: 0;
  display: inline-block;
}

.footer-link:hover {
  /* color: #fff; */
  transform: translateX(5px);
  text-decoration: none !important;
}

/* Social Connect Column */
.social-connect {
  animation: fadeInUp 0.6s ease-out 0.2s forwards;
  opacity: 0;
}

.social-icons {
  display: flex;
  gap: 15px;
  margin-bottom: 30px;
}

.social-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.1);
  color: #fff;
  font-size: 18px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.social-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--accent-terracotta);
  transform: scale(0);
  transition: transform 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border-radius: 50%;
  z-index: -1;
}

.social-icon:hover {
  transform: translateY(-5px);
  color: #fff;
}

.social-icon:hover::before {
  transform: scale(1);
}

.travel-associations {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.association-logo {
  height: 30px;
  opacity: 0.8;
  transition: all 0.3s ease;
}

.association-logo:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* Newsletter Column */
.newsletter {
  animation: fadeInUp 0.6s ease-out 0.4s forwards;
  opacity: 0;
}

.newsletter p {
  color: rgba(255, 255, 255, 0.8);
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 25px;
}

.newsletter-form {
  position: relative;
  overflow: hidden;
  display: flex;
}

.newsletter-form input {
  width: 100%;
  padding: 14px;
  font-size: 14px;
  border: 1px solid #ccc;
  border-radius: 4px 0 0 4px;
  box-sizing: border-box;
}

.newsletter-form input:focus {
  outline: none;
}

.newsletter-button {
  background-color: var(--accent-terracotta);
  color: #fff;
  border: none;
  padding: 14px;
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  border-radius: 0 4px 4px 0;
}

.newsletter-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.7s ease;
}

.newsletter-button:hover {
  background-color: var(--accent-light-brown);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(165, 28, 69, 0.3);
}

.newsletter-button:hover::before {
  left: 100%;
}

/* Footer Bottom */
.footer-bottom {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 25px 0;
  flex-wrap: wrap;
  gap: 20px;
}

.copyright {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  animation: fadeIn 0.8s ease-out forwards;
}

.legal-links {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
}

.legal-link {
  color: rgba(255, 255, 255, 0.6);
  font-size: 13px;
  transition: all 0.3s ease;
  position: relative;
}

.legal-link::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  width: 0;
  height: 1px;
  background-color: var(--primary-green);
  transition: width 0.3s ease;
}

.legal-link:hover {
  color: var(--primary-green);
}

.legal-link:hover::after {
  color: var(--primary-green);
  width: 100%;
}

/* Footer Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive Styles for Footer */
@media (max-width: 992px) {
  .footer-top {
    grid-template-columns: 1fr 1fr;
    gap: 40px 30px;
  }
  
  .social-connect {
    order: 3;
    grid-column: span 2;
  }
  
  .travel-associations {
    justify-content: center;
  }

  .top-bar {
    display: none;
  }
}

@media (max-width: 768px) {
  .footer-top {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .social-connect {
    order: 2;
    grid-column: span 1;
  }
  
  .footer-links {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .social-icons {
    justify-content: center;
  }
  
  .travel-associations {
    justify-content: center;
  }
  
  .footer-bottom {
    flex-direction: column;
    text-align: center;
  }
  
  .legal-links {
    justify-content: center;
  }
}

@media (max-width: 576px) {
  .footer-links {
    grid-template-columns: 1fr;
  }
  
  .footer-top {
    padding: 40px 0 30px;
  }
  
  .legal-links {
    gap: 15px 25px;
  }
}

.about-nav {
  position: static;
}