:root {
    --primary-green: #2a4858;
    --primary-brown: #8B7355;
    --accent-sage: #2a4858ac;
    --accent-terracotta: #967259;
    --accent-light-brown: #A68C69;
    --neutral-cream: #F2E8DC;
    --neutral-beige: #D8C3A5;
    --neutral-light: #F6F4F0;
    --neutral-dark: #3A3026;
    --text-dark: #3A3026;
    --text-medium: #5D4E41;
    --text-light: #F6F4F0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

body {
    background-color: var(--neutral-light);
    color: var(--text-dark);
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}
.container h3{
    padding-left: center;
    place-self: center;
}

/* Header styles */
header {
    background-color: white;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    padding: 15px 0;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
    margin-right: 10px;
}

.logo-text {
    font-weight: bold;
    font-size: 24px;
    color: var(--text-dark);
}

.logo-text span {
    color: var(--accent-terracotta);
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 25px;
}

nav ul li a {
    text-decoration: none;
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--accent-terracotta);
}

.contact-info {
    display: flex;
    align-items: center;
}

.phone {
    margin-right: 15px;
    color: var(--text-medium);
}

.donate-btn {
    background-color: var(--primary-green);
    color: var(--text-light);
    padding: 10px 20px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.donate-btn:hover {
    background-color: var(--accent-sage);
}

/* Hero section */
.hero {
    position: relative;
    padding: 80px 0;
    background-color: #fff;
    overflow: hidden;
}

.hero-content {
    max-width: 600px;
}

.hero-title {
    font-size: 48px;
    font-weight: bold;
    line-height: 1.2;
    margin-bottom: 20px;
    color: var(--text-dark);
}

.hero-text {
    margin-bottom: 30px;
    color: #666;
}

.hero-btn {
    display: inline-block;
    background-color: var(--primary-brown);
    color: var(--text-light);
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.hero-btn:hover {
    background-color: var(--accent-light-brown);
}

.hero-image {
    position: absolute;
    right: 0;
    top: 0;
    height: 100%;
    width: 50%;
    clip-path: polygon(10% 0, 100% 0%, 100% 100%, 0% 100%);
    background-size: cover;
    background-position: center;
}

.shape {
    position: absolute;
    border-radius: 50%;
    opacity: 0.8;
}

.shape-1 {
    width: 20px;
    height: 20px;
    background-color: var(--accent-terracotta);
    top: 20%;
    left: 10%;
}

.shape-2 {
    width: 15px;
    height: 15px;
    background-color: var(--primary-green);
    top: 40%;
    left: 50%;
}

.shape-3 {
    width: 25px;
    height: 25px;
    background-color: var(--primary-brown);
    bottom: 20%;
    left: 30%;
}

/* Programs section */
.programs {
    padding: 60px 0;
    background-color: #fff;
}

.section-title {
    text-align: center;
    font-size: 36px;
    margin-bottom: 10px;
}

.section-subtitle {
    text-align: center;
    color: #666;
    margin-bottom: 40px;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.programs-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-top: 30px;
}

.program-card {
    background-color: var(--neutral-cream);
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.program-card:hover {
    transform: translateY(-10px);
}

.program-img {
    height: 200px;
    width: 100%;
    object-fit: cover;
}

.program-content {
    padding: 20px;
}

.program-title {
    text-align: center;
    font-size: 22px;
    margin-bottom: 10px;
}

.program-text {
    color: #666;
    margin-bottom: 20px;
    font-size: 14px;
}

.learn-more {
    display: inline-block;
    background-color: var(--primary-green);
    color: var(--text-light);
    padding: 8px 20px;
    border-radius: 20px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: background-color 0.3s;
}

.learn-more:hover {
    background-color: var(--accent-sage);
}

/* Welcome section */
.welcome {
    padding: 80px 0;
    text-align: center;
}

.welcome-title {
    font-size: 36px;
    margin-bottom: 40px;
    position: relative;
    display: inline-block;
}

.welcome-title::before {
    content: "";
    position: absolute;
    height: 4px;
    width: 60px;
    background-color: var(--accent-terracotta);
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
}

.welcome-text {
    max-width: 800px;
    margin: 0 auto 40px;
    color: #666;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 60px;
}

.cta-btn-primary {
    background-color: var(--primary-green);
    color: var(--text-light);
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.cta-btn-primary:hover {
    background-color: var(--accent-sage);
}

.cta-btn-secondary {
    background-color: var(--primary-brown);
    color: var(--text-light);
    padding: 12px 30px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: background-color 0.3s;
}

.cta-btn-secondary:hover {
    background-color: var(--accent-light-brown);
}

/* Testimonial section */
.testimonial {
    padding: 60px 0;
    background-color: var(--neutral-cream);
    position: relative;
    overflow: hidden;
}

.testimonial-container {
    display: flex;
    align-items: center;
}

.testimonial-images {
    flex: 1;
    display: flex;
    flex-wrap: wrap;
    position: relative;
    max-width: 400px;
}

.testimonial-img {
    border-radius: 50%;
    object-fit: cover;
}

.testimonial-img-main {
    width: 200px;
    height: 200px;
    z-index: 3;
}

.testimonial-img-1 {
    width: 100px;
    height: 100px;
    position: absolute;
    top: 0;
    right: 50px;
    z-index: 2;
}

.testimonial-img-2 {
    width: 80px;
    height: 80px;
    position: absolute;
    bottom: 30px;
    right: 0;
    z-index: 2;
}

.testimonial-img-3 {
    width: 120px;
    height: 120px;
    position: absolute;
    bottom: 0;
    left: 30px;
    z-index: 2;
}

.testimonial-content {
    flex: 1;
    padding-left: 50px;
}

.testimonial-title {
    font-size: 36px;
    margin-bottom: 20px;
}

.testimonial-subtitle {
    font-size: 18px;
    color: #666;
    margin-bottom: 30px;
}

.testimonial-text {
    color: #666;
    margin-bottom: 30px;
}

.testimonial-link {
    color: var(--accent-terracotta);
    text-decoration: none;
    font-weight: 500;
}

.testimonial-link:hover {
    text-decoration: underline;
}

.shape-4 {
    width: 20px;
    height: 20px;
    background-color: var(--accent-terracotta);
    top: 20%;
    right: 10%;
}

.shape-5 {
    width: 15px;
    height: 15px;
    background-color: var(--primary-green);
    bottom: 40%;
    right: 30%;
}

.shape-6 {
    width: 25px;
    height: 25px;
    background-color: var(--primary-brown);
    bottom: 20%;
    right: 60%;
}

/* Features section */
.features {
    padding: 80px 0;
    background-color: #fff;
}

.features-title {
    font-size: 36px;
    margin-bottom: 60px;
    place-self: center;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

.feature-item {
    text-align: center;
}

.feature-icon {
    width: 60px;
    height: 60px;
    background-color: var(--neutral-beige);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    color: var(--primary-green);
}



.feature-text {
    font-weight: 500;
}

.features-image {
    width: 100%;
    border-radius: 10px;
    margin-top: 30px;
    height: 300px;
    object-fit: cover;
}

@media screen and (max-width: 992px) {
    .hero-title {
        font-size: 36px;
    }
    
    .hero-image {
        width: 45%;
    }
    
    .programs-grid, .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media screen and (max-width: 768px) {
    .header-container {
        flex-direction: column;
        text-align: center;
    }
    
    nav ul {
        margin: 20px 0;
    }
    
    .hero {
        padding: 40px 0;
    }
    
    .hero-content {
        max-width: 100%;
        text-align: center;
    }
    
    .hero-image {
        position: relative;
        width: 100%;
        height: 300px;
        clip-path: none;
        margin-top: 30px;
    }
    
    .testimonial-container {
        flex-direction: column;
    }
    
    .testimonial-images {
        margin-bottom: 40px;
    }
    
    .testimonial-content {
        padding-left: 0;
        text-align: center;
    }
    
    .programs-grid, .features-grid {
        grid-template-columns: 1fr;
    }
}