:root {
  --primary-color: #3498db;
  --secondary-color: #2ecc71;
  --dark-color: #2c3e50;
  --light-color: #ecf0f1;
  --danger-color: #e74c3c;
  --warning-color: #f39c12;
  --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

body {
  background-color: #f5f7fa;
  color: #333;
  line-height: 1.6;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 20px;
  color: var(--dark-color);
}
.tours-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  padding: 20px;
}

.tour-card {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.tour-card:hover {
  transform: translateY(-5px);
}

.tour-image {
  height: 200px;
  overflow: hidden;
}

.tour-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.tour-content {
  padding: 15px;
}

.tour-meta {
  display: flex;
  gap: 15px;
  color: #666;
  font-size: 0.9em;
  margin: 10px 0;
}

.tour-description {
  color: #444;
  font-size: 0.95em;
  margin: 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.tour-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 15px;
}

.tour-category {
  background: #e9ecef;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.85em;
}

.tour-actions {
  display: flex;
  gap: 10px;
}

.edit-btn,
.delete-btn {
  border: none;
  background: none;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
}

.edit-btn:hover {
  color: #007bff;
}

.delete-btn:hover {
  color: #dc3545;
}

.tour-badge {
  position: absolute;
  top: 15px;
  right: 15px;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.badge-featured {
  background: var(--primary-color);
  color: white;
}

.badge-new {
  background: var(--secondary-color);
  color: white;
}

.tour-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 10px;
  color: var(--dark-color);
}

.tour-location {
  color: #777;
  font-size: 14px;
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.tour-location i {
  margin-right: 5px;
  color: var(--primary-color);
}

.tour-details {
  display: flex;
  justify-content: flex-end;
  padding-top: 15px;
  border-top: 1px solid #eee;
}

.add-tour-button {
  margin: 20px 0;
  text-align: right;
  padding: 0 20px;
}

.add-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #2563eb;
  color: white;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.add-btn:hover {
  background-color: #1d4ed8;
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(37, 99, 235, 0.2);
}

.add-btn i {
  font-size: 16px;
}

@media (max-width: 768px) {
  .filters {
    flex-direction: column;
    gap: 15px;
  }

  .search-box {
    max-width: 100%;
  }

  .filter-group {
    justify-content: space-between;
    width: 100%;
  }

  .tours-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  }
}

@media (max-width: 480px) {
  .tours-grid {
    grid-template-columns: 1fr;
  }
}

/* Spinner for loading state */
.loader {
  display: none;
  text-align: center;
  padding: 30px 0;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  animation: spin 1s ease-in-out infinite;
  margin: 0 auto;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
