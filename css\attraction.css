/* Attraction Page Styles */

/* Hero Section */
.attraction-hero {
    height: 70vh;
    background-size: cover;
    background-position: center;
    display: flex;
    align-items: center;
    color: var(--text-light);
    position: relative;
}

.attraction-hero::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
}

.attraction-hero .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
    justify-content: center;
    position: relative;
    position: relative;
    z-index: 2;
    text-align: center;
}

.attraction-hero h1 {
    font-size: 3.5rem;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.location-badge {
    display: inline-block;
    background-color: rgba(0, 0, 0, 0.6);
    padding: 0.5rem 1rem;
    border-radius: 50px;
    font-size: 1.1rem;
}

.location-badge i {
    margin-right: 0.5rem;
    color: var(--accent-terracotta);
}

/* Main Content */
.attraction-content {
    padding: 4rem 0;
    background-color: var(--neutral-light);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.attraction-content .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.about-section {
    flex-direction: column;
}

.attraction-content section {
    margin-bottom: 4rem;
}

.attraction-content h2 {
    font-size: 2.2rem;
    color: var(--text-dark);
    margin-bottom: 1.5rem;
    position: relative;
    padding-bottom: 0.5rem;
}

.attraction-content h2::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 80px;
    height: 3px;
    background-color: var(--accent-terracotta);
}

/* About Section */
.about-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-medium);
}

.about-content p {
    margin-bottom: 1.5rem;
    text-align: justify;
}

/* Activities Section */
.activities-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 2rem;
}

.activity-card {
    background-color: var(--neutral-cream);
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.activity-icon {
    font-size: 2.5rem;
    color: var(--primary-brown);
    margin-bottom: 1rem;
}

.activity-card h3 {
    font-size: 1.2rem;
    color: var(--text-dark);
}

/* Gallery Section */
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    cursor: pointer;
    transition: transform 0.3s ease;
}

.gallery-item:hover {
    transform: scale(1.02);
}

.gallery-item img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    display: block;
}

.image-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: var(--text-light);
    padding: 0.8rem;
    font-size: 0.9rem;
    transform: translateY(100%);
    transition: transform 0.3s ease;
}

.gallery-item:hover .image-caption {
    transform: translateY(0);
}

.no-images {
    text-align: center;
    color: var(--text-medium);
    font-style: italic;
    padding: 2rem;
    background-color: var(--neutral-cream);
    border-radius: 8px;
}

/* Lightbox */
.lightbox {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 80vh;
    border: 3px solid var(--text-light);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

.lightbox-caption {
    color: var(--text-light);
    text-align: center;
    padding: 1rem 0;
    font-size: 1.1rem;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: 0;
    color: var(--text-light);
    font-size: 2rem;
    cursor: pointer;
}

/* External Link Section */
.external-link-section {
    text-align: center;
    padding: 2rem;
    background-color: var(--neutral-cream);
    border-radius: 8px;
}

.external-link-button {
    display: inline-block;
    background-color: var(--primary-green);
    color: var(--text-light);
    padding: 1rem 2rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    margin-top: 1rem;
    transition: background-color 0.3s ease;
}

.external-link-button:hover {
    background-color: var(--accent-sage);
}

.external-link-button i {
    margin-right: 0.5rem;
}

/* CTA Section */
.attraction-cta {
    background-color: var(--primary-green);
    color: var(--text-light);
    padding: 4rem 2rem;
    border-radius: 8px;
    text-align: center;
    margin-top: 3rem;
}

.cta-content h2 {
    color: var(--text-light);
    font-size: 2.5rem;
    margin-bottom: 1rem;
}

.cta-content h2::after {
    display: none;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 700px;
    margin-left: auto;
    margin-right: auto;
}

.cta-buttons {
    display: flex;
    justify-content: center;
    gap: 1.5rem;
    flex-wrap: wrap;
}

.cta-primary, .cta-secondary {
    display: inline-block;
    padding: 1rem 2.5rem;
    border-radius: 50px;
    text-decoration: none;
    font-weight: bold;
    transition: all 0.3s ease;
}

.cta-primary {
    background-color: var(--accent-terracotta);
    color: var(--text-light);
}

.cta-primary:hover {
    background-color: var(--primary-brown);
    transform: translateY(-3px);
}

.cta-secondary {
    background-color: transparent;
    color: var(--text-light);
    border: 2px solid var(--text-light);
}

.cta-secondary:hover {
    background-color: rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .attraction-hero h1 {
        font-size: 2.8rem;
    }
    
    .attraction-content h2 {
        font-size: 2rem;
    }
    
    .cta-content h2 {
        font-size: 2.2rem;
    }
}

@media (max-width: 768px) {
    .attraction-hero {
        height: 50vh;
    }
    
    .attraction-hero h1 {
        font-size: 2.2rem;
    }
    
    .activities-grid {
        grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    }
    
    .gallery-grid {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }
    
    .cta-content h2 {
        font-size: 1.8rem;
    }
}

@media (max-width: 576px) {
    .attraction-hero {
        height: 40vh;
    }
    
    .attraction-hero h1 {
        font-size: 1.8rem;
    }
    
    .location-badge {
        font-size: 0.9rem;
    }
    
    .attraction-content {
        padding: 2rem 0;
    }
    
    .attraction-content h2 {
        font-size: 1.6rem;
    }
    
    .about-content {
        font-size: 1rem;
    }
    
    .activities-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .cta-buttons {
        flex-direction: column;
        gap: 1rem;
    }
    
    .cta-primary, .cta-secondary {
        width: 100%;
    }
}

