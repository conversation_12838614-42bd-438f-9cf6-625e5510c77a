/* Contact Page Styles */
/* Comprehensive styling for the Virunga Ecotours Community Contact Page */

/* Page Header Styles */
.page-header {
    position: relative;
    height: 40vh;
    min-height: 280px;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    margin-bottom: 0;
}

.page-header-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -2;
}

.page-header-background img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    filter: brightness(0.7);
}

.page-header-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(42, 72, 88, 0.8) 0%,
        rgba(42, 72, 88, 0.6) 50%,
        rgba(42, 72, 88, 0.8) 100%
    );
    z-index: -1;
}

.page-header-content {
    text-align: center;
    color: white;
    z-index: 1;
    max-width: 800px;
    padding: 0 20px;
}

.page-header-content h1 {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    line-height: 1.2;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
}

.page-header-content p {
    font-size: 1rem;
    opacity: 0.95;
    line-height: 1.5;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    max-width: 500px;
    margin: 0 auto;
}

/* Breadcrumb Navigation */
.breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.breadcrumb a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.breadcrumb a:hover {
    color: white;
}

.breadcrumb .separator {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8rem;
}

.breadcrumb .current {
    color: white;
    font-weight: 600;
}

/* Contact Section */
.contact-section {
    padding: 40px 0;
    background-color: var(--neutral-light);
}

.contact-grid {
    display: grid;
    grid-template-columns: 3fr 2fr;
    gap: 2rem;
    align-items: start;
}

/* Contact Form Container */
.contact-form-container {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1.5rem;
    box-shadow: var(--shadow-md);
    position: relative;
    overflow: hidden;
}

.contact-form-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(
        90deg,
        var(--primary-green) 0%,
        var(--accent-terracotta) 50%,
        var(--accent-light-brown) 100%
    );
}

.form-header {
    margin-bottom: 2rem;
    text-align: center;
}

.form-header h2 {
    font-size: 1.5rem;
    color: var(--primary-green);
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.form-header p {
    color: var(--text-medium);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Success and Error Messages */
.success-message,
.error-message {
    padding: 1.5rem;
    border-radius: var(--border-radius-md);
    margin-bottom: 2rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    animation: slideInDown 0.5s ease;
}

.success-message {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    border: 1px solid #c3e6cb;
    color: #155724;
}

.error-message {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    border: 1px solid #f5c6cb;
    color: #721c24;
}

.success-message i,
.error-message i {
    font-size: 1.5rem;
    flex-shrink: 0;
}

.success-message h3 {
    margin: 0 0 0.5rem 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.success-message p,
.error-message p {
    margin: 0;
    line-height: 1.5;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Styles */
.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-dark);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 8px 12px;
    border: 1px solid #e0e0e0;
    border-radius: var(--border-radius-sm);
    font-size: 0.9rem;
    font-family: inherit;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-green);
    background-color: white;
    box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.1);
    transform: translateY(-1px);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

.form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='currentColor' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6,9 12,15 18,9'%3e%3c/polyline%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px;
    padding-right: 40px;
    appearance: none;
}

/* Checkbox Group */
.checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-top: 0.5rem;
}

.checkbox-label {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    font-weight: 500;
    color: var(--text-medium);
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    transition: background-color 0.3s ease;
}

.checkbox-label:hover {
    background-color: rgba(42, 72, 88, 0.05);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: var(--border-radius-sm);
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-green);
    border-color: var(--primary-green);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Submit Button */
.submit-btn {
    margin-top: 1rem;
    padding: 10px 20px;
    font-size: 0.9rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.3px;
    border: none;
    border-radius: var(--border-radius-sm);
    background: linear-gradient(135deg, var(--primary-green) 0%, var(--accent-sage) 100%);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(42, 72, 88, 0.3);
}

.submit-btn:active {
    transform: translateY(0);
}

.submit-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.submit-btn:hover::before {
    left: 100%;
}

/* Quick Actions Section */
.quick-actions {
    background: white;
    border-radius: var(--border-radius-md);
    padding: 1.25rem;
    box-shadow: var(--shadow-sm);
    border-left: 3px solid var(--accent-light-brown);
}

.quick-actions h4 {
    font-size: 1rem;
    color: var(--primary-green);
    margin-bottom: 1rem;
    font-weight: 600;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.contact-action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    background: linear-gradient(135deg, var(--neutral-light) 0%, #f8f9fa 100%);
    border: 1px solid transparent;
    border-radius: var(--border-radius-sm);
    color: var(--primary-green);
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.contact-action-btn:hover {
    background: var(--primary-green);
    color: white;
    transform: translateX(5px);
    box-shadow: var(--shadow-md);
}

.contact-action-btn i {
    font-size: 1.1rem;
    width: 20px;
    text-align: center;
}

/* Map Section */
.map-section {
    padding: 40px 0;
    background: white;
}

.map-section .section-header {
    margin-bottom: 2rem;
}

.map-section .section-header h2 {
    font-size: 1.75rem;
    color: var(--primary-green);
    margin-bottom: 0.75rem;
    font-weight: 600;
}

.map-section .section-header p {
    font-size: 1.1rem;
    color: var(--text-medium);
    max-width: 600px;
    margin: 0 auto;
}

.map-container {
    position: relative;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    max-width: 900px;
    margin: 0 auto;
}

.map-placeholder {
    position: relative;
    width: 100%;
    height: 550px;
}

.map-placeholder img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.map-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        135deg,
        rgba(42, 72, 88, 0.7) 0%,
        rgba(42, 72, 88, 0.5) 50%,
        rgba(42, 72, 88, 0.7) 100%
    );
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.map-container:hover .map-overlay {
    opacity: 1;
}

.map-info {
    text-align: center;
    color: white;
    padding: 2rem;
}

.map-info h3 {
    font-size: 1.8rem;
    margin-bottom: 1rem;
    font-weight: 600;
}

.map-info p {
    font-size: 1rem;
    line-height: 1.6;
    opacity: 0.95;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .contact-grid {
        grid-template-columns: 1fr;
        gap: 3rem;
    }

    .contact-form-container {
        padding: 2rem;
    }

    .social-links {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .page-header {
        height: 50vh;
        min-height: 350px;
    }

    .page-header-content h1 {
        font-size: 2.5rem;
    }

    .page-header-content p {
        font-size: 1.1rem;
    }

    .contact-section {
        padding: 60px 0;
    }

    .contact-form-container {
        padding: 1.5rem;
    }

    .form-header h2 {
        font-size: 1.8rem;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .contact-info,
    .social-media,
    .quick-actions {
        padding: 1.5rem;
    }

    .contact-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .contact-icon {
        align-self: center;
    }

    .map-section {
        padding: 60px 0;
    }

    .map-section .section-header h2 {
        font-size: 2rem;
    }

    .map-placeholder {
        height: 300px;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .page-header-content h1 {
        font-size: 2rem;
    }

    .breadcrumb {
        flex-direction: column;
        gap: 0.25rem;
    }

    .breadcrumb .separator {
        display: none;
    }

    .contact-form-container {
        padding: 1rem;
    }

    .form-header h2 {
        font-size: 1.5rem;
    }

    .submit-btn {
        padding: 14px 24px;
        font-size: 0.9rem;
    }

    .contact-info h3,
    .social-media h4,
    .quick-actions h4 {
        font-size: 1.1rem;
    }

    .map-placeholder {
        height: 250px;
    }

    .map-info h3 {
        font-size: 1.5rem;
    }

    .map-info p {
        font-size: 0.9rem;
    }
}

/* Print Styles */
@media print {
    .page-header,
    .map-section,
    .social-media,
    .quick-actions {
        display: none;
    }

    .contact-section {
        padding: 20px 0;
    }

    .contact-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .contact-form-container,
    .contact-info {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .contact-form-container,
    .contact-info,
    .social-media,
    .quick-actions {
        border: 2px solid var(--text-dark);
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        border: 2px solid var(--text-dark);
    }

    .submit-btn {
        border: 2px solid var(--text-dark);
    }
}

/* Loading States */
.submit-btn.loading {
    pointer-events: none;
    opacity: 0.7;
}

.submit-btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Custom Scrollbar for Form Elements */
.form-group textarea::-webkit-scrollbar {
    width: 8px;
}

.form-group textarea::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.form-group textarea::-webkit-scrollbar-thumb {
    background: var(--primary-green);
    border-radius: 4px;
}

.form-group textarea::-webkit-scrollbar-thumb:hover {
    background: var(--accent-sage);
}

/* Tooltip Styles for Form Help */
.form-tooltip {
    position: relative;
    display: inline-block;
    margin-left: 0.5rem;
    color: var(--text-medium);
    cursor: help;
}

.form-tooltip:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--text-dark);
    color: white;
    padding: 0.5rem;
    border-radius: var(--border-radius-sm);
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
}

/* Enhanced Button Interactions */
.submit-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.submit-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

/* Contact Item Animations */
.contact-item {
    animation: fadeInUp 0.6s ease forwards;
    opacity: 0;
    transform: translateY(20px);
}

.contact-item:nth-child(1) { animation-delay: 0.1s; }
.contact-item:nth-child(2) { animation-delay: 0.2s; }
.contact-item:nth-child(3) { animation-delay: 0.3s; }
.contact-item:nth-child(4) { animation-delay: 0.4s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Form Field Focus Ring Enhancement */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    box-shadow: 0 0 0 3px rgba(42, 72, 88, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Social Link Hover Effects Enhancement */
.social-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: currentColor;
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: inherit;
    z-index: -1;
}

.social-link:hover::before {
    opacity: 0.1;
}
