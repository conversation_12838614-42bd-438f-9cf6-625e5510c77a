.content-wrapper {
    padding: 2rem;
    max-width: 1400px;
    margin: 0 auto;
}

.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2rem;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.page-header p {
    color: #666;
    font-size: 1.1rem;
}

.submissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 2rem;
}

.submission-card {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.submission-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.card-header {
    padding: 1.5rem;
    background: #2c3e50;
    color: white;
}

.card-header h2 {
    margin: 0;
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.card-header time {
    font-size: 0.9rem;
    opacity: 0.8;
}

.card-content {
    padding: 1.5rem;
}

.card-content section {
    margin-bottom: 1.5rem;
}

.card-content h3 {
    color: #2c3e50;
    font-size: 1.1rem;
    margin-bottom: 0.75rem;
}

.card-content ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.card-content li {
    margin-bottom: 0.5rem;
    color: #555;
}

.card-content li strong {
    color: #2c3e50;
}

.additional-info {
    border-top: 1px solid #eee;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
}

.additional-info summary {
    color: #3498db;
    cursor: pointer;
    font-weight: 500;
    list-style: none;
}

.additional-info summary::-webkit-details-marker {
    display: none;
}

.additional-info summary::after {
    content: '▼';
    margin-left: 0.5rem;
    font-size: 0.8rem;
}

.additional-info[open] summary::after {
    content: '▲';
}

.details-content {
    margin-top: 1rem;
}

.details-content p {
    color: #555;
    line-height: 1.6;
    margin: 0.5rem 0;
}

.no-submissions {
    text-align: center;
    padding: 3rem;
    background: #f8f9fa;
    border-radius: 8px;
    color: #666;
}

.no-submissions p {
    font-size: 1.1rem;
    margin: 0;
}

@media (max-width: 768px) {
    .content-wrapper {
        padding: 1rem;
    }

    .submissions-grid {
        grid-template-columns: 1fr;
    }

    .card-header {
        padding: 1rem;
    }

    .card-content {
        padding: 1rem;
    }
}