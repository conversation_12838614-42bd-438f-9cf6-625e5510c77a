.profile-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.profile-header {
  background: var(--neutral-light);
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  overflow: hidden;
  margin-bottom: 30px;
}

.cover-photo {
  height: 200px;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-sage));
  position: relative;
}

.profile-avatar {
  position: absolute;
  bottom: -50px;
  left: 50px;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  border: 5px solid var(--neutral-light);
  overflow: hidden;
  box-shadow: var(--box-shadow);
  transition: transform 0.3s ease;
}

.profile-avatar:hover {
  transform: scale(1.05);
}

.profile-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-edit {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 40px;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.profile-avatar:hover .avatar-edit {
  opacity: 1;
}

.profile-info {
  padding: 60px 30px 30px;
}

.profile-info h1 {
  margin: 0;
  font-size: 24px;
  color: var(--text-dark);
}

.profile-info p {
  margin: 5px 0 0;
  color: var(--text-medium);
}

.profile-content {
  background: var(--neutral-light);
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.profile-nav {
  display: flex;
  border-bottom: 1px solid var(--neutral-beige);
  padding: 0 20px;
}

.profile-nav button {
  padding: 15px 25px;
  border: none;
  background: none;
  color: var(--text-medium);
  font-size: 16px;
  cursor: pointer;
  position: relative;
  transition: color 0.3s ease;
}

.profile-nav button.active {
  color: var(--primary-green);
}

.profile-nav button.active::after {
  content: "";
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--primary-green);
}

.profile-section {
  display: none;
  padding: 30px;
}

.profile-section.active {
  display: block;
  animation: fadeIn 0.5s ease;
}

/* Image Upload Styles */
.image-upload-group {
  grid-column: 1 / -1;
  margin-bottom: 1.5rem;
}

.image-upload-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.image-preview {
  position: relative;
  width: 150px;
  height: 150px;
  border-radius: 50%;
  overflow: hidden;
  border: 3px solid var(--neutral-beige);
  margin: 0 auto;
  cursor: pointer;
  transition: all var(--transition-speed) ease;
}

.image-preview:hover {
  border-color: var(--primary-green);
  transform: scale(1.05);
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-preview:hover .preview-overlay {
  opacity: 1;
}

.upload-options {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
}

.upload-btn {
  padding: 0.75rem 1.5rem;
  background-color: var(--primary-green);
  color: white;
  border-radius: var(--border-radius);
  font-weight: 500;
  transition: all var(--transition-speed) ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.upload-btn:hover {
  background-color: var(--accent-sage);
  transform: translateY(-2px);
}

.drag-text {
  color: var(--text-medium);
  font-size: 0.9rem;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  color: var(--text-medium);
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 10px;
  border: 1px solid var(--neutral-beige);
  border-radius: 5px;
  font-size: 16px;
  background: var(--neutral-light);
  color: var(--text-dark);
  transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus {
  border-color: var(--primary-green);
  outline: none;
}

.save-btn {
  background: var(--primary-green);
  color: var(--text-light);
  border: none;
  padding: 12px 25px;
  border-radius: 5px;
  cursor: pointer;
  font-size: 16px;
  transition: background 0.3s ease;
}

.save-btn:hover {
  background: var(--accent-sage);
}

.switch-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--neutral-beige);
  transition: 0.4s;
}

.slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  transition: 0.4s;
}

.switch input:checked + .slider {
  background-color: var(--primary-green);
}

switch input:checked + .slider:before {
  transform: translateX(26px);
}

slider.round {
  border-radius: 34px;
}

slider.round:before {
  border-radius: 50%;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
