/* About Page Manager Styles */

.page-header {
    margin-bottom: 30px;
    padding: 20px;
    border-bottom: 2px solid var(--primary-green);
}

.page-header h1 {
    color: var(--primary-green);
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
}

.page-header p {
    color: var(--text-medium);
    font-size: 1.1rem;
    margin: 0;
}

/* Tabs Styling */
.tabs-container {
    margin-bottom: 30px;
    padding: 20px;
}

.tabs {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
    border-bottom: 2px solid #e0e0e0;
    padding-bottom: 0;
}

.tab-btn {
    background: none;
    border: none;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    color: var(--text-medium);
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    position: relative;
}

.tab-btn:hover {
    background-color: #f5f5f5;
    color: var(--primary-green);
}

.tab-btn.active {
    background-color: var(--primary-green);
    color: white;
    font-weight: 600;
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background-color: var(--primary-green);
}

/* Tab Content */
.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in-out;
    padding: 20px;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Section Cards */
.section-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.section-header {
    background: var(--primary-green);
    color: white;
    padding: 25px 30px;
    border-bottom: 1px solid #e0e0e0;
}

.section-header h2 {
    margin: 0 0 8px 0;
    font-size: 1.8rem;
    display: flex;
    align-items: center;
    gap: 12px;
    color: white;
}

.section-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 1rem;
}

/* Forms */
.section-form {
    padding: 30px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: var(--text-dark);
    font-size: 14px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    transition: all 0.3s ease;
    background-color: #fafafa;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-green);
    background-color: white;
    box-shadow: 0 0 0 3px rgba(46, 125, 50, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Current Image Display */
.current-image {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.current-image img {
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.current-image p {
    margin: 8px 0 0 0;
    font-size: 12px;
    color: var(--text-medium);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    text-align: center;
    justify-content: center;
}

.btn-primary {
    background-color: var(--primary-green);
    color: white;
}

.btn-primary:hover {
    background-color: #2d5a3d;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.3);
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #218838;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
}

.btn-sm {
    padding: 8px 16px;
    font-size: 12px;
}

.form-actions {
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
    text-align: right;
}

/* Subsections */
.subsection {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #f0f0f0;
}

.subsection h3 {
    color: var(--primary-green);
    font-size: 1.4rem;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Grid Layouts */
.stats-grid,
.team-grid,
.values-grid,
.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card,
.team-card,
.value-card,
.gallery-card {
    background: #f8f9fa;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 20px;
    transition: all 0.3s ease;
}

.stat-card:hover,
.team-card:hover,
.value-card:hover,
.gallery-card:hover {
    border-color: var(--primary-green);
    box-shadow: 0 4px 12px rgba(46, 125, 50, 0.1);
    transform: translateY(-2px);
}

/* Add New Section */
.add-new-section {
    text-align: center;
    margin-top: 30px;
    padding: 20px;
    border: 2px dashed #ccc;
    border-radius: 10px;
    background-color: #fafafa;
}

.add-new-section:hover {
    border-color: var(--primary-green);
    background-color: #f0f8f0;
}

/* Message Receiver */
.message-receiver {
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    font-weight: 500;
}

.message-receiver.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message-receiver.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Design */
@media (max-width: 768px) {
    .form-row {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .tabs {
        flex-direction: column;
    }
    
    .tab-btn {
        border-radius: 8px;
        margin-bottom: 5px;
    }
    
    .stats-grid,
    .team-grid,
    .values-grid,
    .gallery-grid {
        grid-template-columns: 1fr;
    }
    
    .section-form {
        padding: 20px;
    }
    
    .page-header h1 {
        font-size: 2rem;
    }
}
