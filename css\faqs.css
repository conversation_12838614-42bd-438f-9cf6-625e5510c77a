
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: var(--text-dark);
  background-color: var(--neutral-cream);
}

.hero {
  background-image: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
  url("../images/hero/about.jpg");
  background-size: cover;
  background-position: center;
  color: white;
  padding: 5rem 2rem;
  text-align: center;
  position: relative;
  height: 80vh;
}

.hero h1 {
  font-size: 2.5rem;
  margin-bottom: 1rem;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero p {
  max-width: 800px;
  margin: 0 auto;
  font-size: 1.1rem;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.section-title {
  text-align: center;
  margin-bottom: 2rem;
  color: var(--primary-green);
  position: relative;
  padding-bottom: 0.5rem;
}

.section-title::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 100px;
  height: 3px;
  background: var(--primary-brown);
}

.faq-section {
  margin-bottom: 3rem;
  background-color: white;
  border-radius: 10px;
  box-shadow: var(--shadow);
  padding: 2rem;
  opacity: 0;
  transform: translateY(20px);
  animation: fadeIn 0.5s ease forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.faq-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.faq-item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  overflow: hidden;
  transition: var(--transition);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  background-color: white;
}

.faq-item:hover {
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.faq-question {
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  font-weight: 500;
  color: var(--text-dark);
  transition: var(--transition);
}

.faq-question:hover {
  background-color: rgba(42, 109, 80, 0.05);
}

.faq-question i {
  transition: var(--transition);
  color: var(--primary-color);
}

.faq-answer {
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.5s ease;
  padding: 0 1rem;
}

.faq-answer-content {
  padding-bottom: 1rem;
  color: var(--text-medium);
}

.faq-item.active .faq-question {
  background-color: rgba(42, 109, 80, 0.1);
}

.faq-item.active .faq-question i {
  transform: rotate(180deg);
}

.faq-item.active .faq-answer {
  max-height: 500px;
}

.contact-form {
  background: var(--primary-green);
  padding: 2rem;
  border-radius: 10px;
  color: var(--text-light);
  margin-top: 3rem;
}

.form-group {
  margin-bottom: 1rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.form-control {
  width: 100%;
  padding: 0.8rem;
  border: 1px solid #ddd;
  border-radius: 5px;
  font-size: 1rem;
}

.form-row {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
}

.form-row .form-group {
  flex: 1;
  margin-bottom: 0;
}

.btn {
  display: inline-block;
  padding: 0.8rem 1.5rem;
  background-color: var(--accent-color);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 1rem;
  transition: var(--transition);
  text-align: center;
  width: 100%;
  font-weight: 600;
}

.btn:hover {
  background-color: #c0392b;
  transform: translateY(-2px);
}

footer {
  background-color: var(--neutral-dark);
  color: var(--text-light);
  text-align: center;
  padding: 1.5rem;
  margin-top: 2rem;
}

.nav-links {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin: 1.5rem 0;
}

.nav-links a {
  color: var(--primary-color);
  text-decoration: none;
  font-weight: 500;
  transition: var(--transition);
}

.nav-links a:hover {
  color: var(--secondary-color);
}

.g-recaptcha {
  display: inline-block;
  margin: 1rem 0;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
    gap: 0;
  }

  .hero {
    padding: 3rem 1rem;
  }

  .hero h1 {
    font-size: 2rem;
  }

  .container {
    padding: 1rem;
  }

  .faq-section {
    padding: 1.5rem;
  }

  .contact-form {
    padding: 1.5rem;
  }
}
