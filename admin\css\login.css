.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.5)),
    url("../../images/hero/gorille.jpg");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

.login-card {
  background: var(--neutral-light);
  border-radius: 4px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 400px;
  padding: 1rem 2.5rem;
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.card-header {
  text-align: center;
  margin-bottom: 2rem;
}

.card-header .logo {
  width: 80px;
  height: 80px;
  margin-bottom: 1.5rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.card-header h1 {
  color: var(--text-dark);
  font-size: 1.8rem;
  margin-bottom: 0.5rem;
}

.card-header p {
  color: var(--text-medium);
  font-size: 0.9rem;
}

.form-group {
  margin-bottom: 1.5rem;
}

.input-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon i {
  position: absolute;
  left: 1rem;
  color: var(--text-medium);
  transition: color 0.3s ease;
}

.input-icon input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: 2px solid var(--neutral-beige);
  border-radius: 10px;
  transition: all 0.3s ease;
  font-size: 0.95rem;
}

.input-icon input:focus {
  border-color: var(--primary-green);
  box-shadow: 0 0 0 3px rgba(74, 103, 65, 0.1);
}

.input-icon input:focus + i {
  color: var(--primary-green);
}

.toggle-password {
  position: absolute;
  right: 3rem;
  top: 40%;
  color: var(--text-medium);
  background: none;
  border: none;
  cursor: pointer;
  transition: color 0.3s ease;
}

.toggle-password:hover {
  color: var(--primary-green);
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.9rem;
}

.remember-me {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.remember-me input {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--neutral-beige);
  border-radius: 4px;
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkmark:before {
  content: "\f00c";
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
  font-size: 12px;
  color: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.remember-me input:checked + .checkmark {
  background-color: var(--primary-green);
  border-color: var(--primary-green);
}

.remember-me input:checked + .checkmark:before {
  opacity: 1;
}

.forgot-password {
  color: var(--primary-green);
  text-decoration: none;
  transition: color 0.3s ease;
}

.forgot-password:hover {
  color: var(--accent-sage);
  text-decoration: underline;
}

.login-btn {
  width: 100%;
  padding: 1rem;
  background: var(--primary-green);
  color: white;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
}

.login-btn:hover {
  background: var(--accent-sage);
  transform: translateY(-2px);
}

.login-btn:active {
  transform: translateY(0);
}

.back-to-site {
  text-align: center;
  margin-top: 2rem;
}

.back-to-site a {
  color: var(--text-medium);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: color 0.3s ease;
}

.back-to-site a:hover {
  color: var(--primary-green);
}

@media (max-width: 480px) {
  .login-card {
    padding: 2rem;
  }

  .card-header .logo {
    width: 60px;
    height: 60px;
  }

  .card-header h1 {
    font-size: 1.5rem;
  }

  .remember-forgot {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 15px 25px;
  border-radius: 4px;
  color: white;
  font-size: 0.9rem;
  opacity: 0;
  transform: translateY(-20px);
  transition: all 0.3s ease;
  z-index: 1000;
}

.notification.show {
  opacity: 1;
  transform: translateY(0);
}

.notification.success {
  background-color: var(--primary-green);
  box-shadow: 0 4px 12px rgba(74, 103, 65, 0.2);
}

.notification.error {
  background-color: #dc3545;
  box-shadow: 0 4px 12px rgba(220, 53, 69, 0.2);
}
