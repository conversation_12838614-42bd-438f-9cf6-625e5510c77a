.blog-view-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 2rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  font-family: 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', sans-serif;
}

.blog-view-header {
  margin-bottom: 2.5rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.blog-view-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2a4858;
  margin-bottom: 0.8rem;
  line-height: 1.3;
}

.blog-meta {
  display: flex;
  gap: 1.5rem;
  color: #6c757d;
  font-size: 0.95rem;
  margin-bottom: 1rem;
}

.blog-meta span {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.blog-view-cover {
  margin: 2rem 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.blog-view-cover img {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.blog-view-cover:hover img {
  transform: scale(1.02);
}

.blog-view-content {
  line-height: 1.8;
  color: #495057;
  font-size: 1.1rem;
}

.blog-intro {
  margin-bottom: 3rem;
}

.blog-intro h2 {
  font-size: 1.8rem;
  color: #2a4858;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.blog-intro p {
  margin-bottom: 1.5rem;
  font-size: 1.15rem;
  line-height: 1.8;
  color: #495057;
}

.content-block {
  margin-bottom: 2.5rem;
}

.content-block h3 {
  font-size: 1.5rem;
  color: #2a4858;
  margin-bottom: 1.2rem;
  font-weight: 600;
}

.content-block p {
  margin-bottom: 1.5rem;
  line-height: 1.8;
}

.text-block {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  border-left: 4px solid #8b7355;
}

.image-block {
  margin: 2rem 0;
}

.image-block img {
  width: 100%;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.image-caption {
  text-align: center;
  font-size: 0.9rem;
  color: #6c757d;
  margin-top: 0.5rem;
  font-style: italic;
}

.quote-block {
  background: #f8f9fa;
  padding: 2rem;
  border-radius: 8px;
  margin: 2rem 0;
  position: relative;
}

.quote-block::before {
  content: '"';
  position: absolute;
  font-size: 5rem;
  color: rgba(139, 115, 85, 0.1);
  top: -1rem;
  left: 1rem;
  font-family: Georgia, serif;
}

.quote-block blockquote {
  font-size: 1.3rem;
  line-height: 1.6;
  color: #2a4858;
  font-style: italic;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
}

.quote-block cite {
  display: block;
  text-align: right;
  font-size: 0.9rem;
  color: #6c757d;
}

.list-block ul.content-list {
  list-style-type: none;
  padding-left: 0;
}

.list-block ul.content-list li {
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
}

.list-block ul.content-list li::before {
  content: "";
  position: absolute;
  left: 0;
  top: 1.1rem;
  width: 8px;
  height: 8px;
  background-color: #8b7355;
  border-radius: 50%;
}

.blog-gallery {
  margin: 3rem 0;
}

.blog-gallery h3 {
  font-size: 1.5rem;
  color: #2a4858;
  margin-bottom: 1.5rem;
  font-weight: 600;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.gallery-item {
  height: 180px;
  border-radius: 8px;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.gallery-item:hover {
  transform: translateY(-5px);
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.5s ease;
}

.gallery-item:hover img {
  transform: scale(1.1);
}

.blog-view-footer {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.back-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.8rem 1.5rem;
  background-color: #8b7355;
  color: white;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.back-button:hover {
  background-color: #6d5a43;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(139, 115, 85, 0.2);
}

@media (max-width: 768px) {
  .blog-view-container {
    padding: 1.5rem;
  }
  
  .blog-view-title {
    font-size: 2rem;
  }
  
  .blog-meta {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .gallery-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .blog-view-container {
    padding: 1rem;
  }
  
  .blog-view-title {
    font-size: 1.8rem;
  }
  
  .blog-intro h2 {
    font-size: 1.5rem;
  }
  
  .content-block h3 {
    font-size: 1.3rem;
  }
  
  .quote-block blockquote {
    font-size: 1.1rem;
  }
}
